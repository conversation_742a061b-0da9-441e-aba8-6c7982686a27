
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ThemeProvider } from '../context/ThemeContext';
import Header from '../components/Header';
import ChatSidebar from '../components/ChatSidebar';
import LivePreview from '../components/LivePreview';
import { GeneratedFile, AIModel } from '../types';
import { regenerateSection, modifyWebsiteFromChat } from '../services/aiService';
import { getApiKey } from '../utils/storage';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { useToast } from '@/hooks/use-toast';

const ProjectEditor = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([]);
  const [chatHistory, setChatHistory] = useState<Array<{role: 'user' | 'assistant', content: string}>>([]);
  const [currentModel, setCurrentModel] = useState<AIModel>('openai');
  const [currentPrompt, setCurrentPrompt] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState<boolean>(false);

  useEffect(() => {
    // Get data from navigation state
    const state = location.state as {
      files?: GeneratedFile[];
      history?: Array<{role: 'user' | 'assistant', content: string}>;
      model?: AIModel;
      prompt?: string;
    };

    if (state?.files) {
      setGeneratedFiles(state.files);
      setChatHistory(state.history || []);
      setCurrentModel(state.model || 'openai');
      setCurrentPrompt(state.prompt || '');
      
      console.log('ProjectEditor initialized with:', {
        files: state.files.length,
        history: state.history?.length || 0,
        model: state.model
      });
    } else {
      // Redirect back to home if no data
      navigate('/');
    }
  }, [location.state, navigate]);

  const handleRegenerateSection = async (sectionId: string, prompt: string) => {
    if (isProcessing) return;
    
    setIsProcessing(true);
    try {
      console.log('Regenerating section:', sectionId, 'with prompt:', prompt);
      
      const updatedFiles = await regenerateSection({
        originalPrompt: currentPrompt,
        sectionPrompt: prompt,
        sectionId,
        currentFiles: generatedFiles,
        model: currentModel,
      });
      
      setGeneratedFiles(updatedFiles);
      
      // Add to chat history
      setChatHistory(prev => [
        ...prev,
        { role: 'user', content: `Regenerate ${sectionId}: ${prompt}` },
        { role: 'assistant', content: `I've successfully regenerated the ${sectionId} section based on your request.` }
      ]);
      
      toast({
        title: "Section regenerated!",
        description: "The selected section has been updated successfully.",
      });
      
    } catch (err) {
      console.error('Regeneration error:', err);
      toast({
        variant: "destructive",
        title: "Regeneration failed",
        description: err instanceof Error ? err.message : 'Failed to regenerate section',
      });
      throw err;
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNewMessage = async (message: string) => {
    if (isProcessing) return;

    setIsProcessing(true);

    // Add user message to history immediately
    setChatHistory(prev => [...prev, { role: 'user', content: message }]);

    try {
      console.log('Processing chat message:', message);

      // Get the API key for the current model
      const apiKey = getApiKey(currentModel);

      // Use the new chat modification function
      const updatedFiles = await modifyWebsiteFromChat({
        prompt: message,
        currentFiles: generatedFiles,
        chatHistory,
        model: currentModel,
        apiKey
      });

      setGeneratedFiles(updatedFiles);
      
      // Add AI response to history
      setChatHistory(prev => [...prev, { 
        role: 'assistant', 
        content: `I've updated your website based on your request. The changes have been applied and you can see them in the live preview.` 
      }]);
      
      toast({
        title: "Website updated!",
        description: "Your changes have been applied successfully.",
      });
      
    } catch (err) {
      console.error('Chat modification error:', err);
      
      // Add error response to history
      setChatHistory(prev => [...prev, { 
        role: 'assistant', 
        content: `I encountered an issue while processing your request: ${err instanceof Error ? err.message : 'Unknown error'}. Please try rephrasing your request or be more specific about what you'd like to change.` 
      }]);
      
      toast({
        variant: "destructive",
        title: "Update failed",
        description: err instanceof Error ? err.message : 'Failed to update website',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen flex flex-col bg-background text-foreground">
        <Header />
        
        <main className="flex-1 h-[calc(100vh-64px)] overflow-hidden">
          <ResizablePanelGroup direction="horizontal" className="h-full w-full">
            <ResizablePanel defaultSize={35} minSize={20} maxSize={50} className="h-full">
              <ChatSidebar
                history={chatHistory}
                onSendMessage={handleNewMessage}
                files={generatedFiles}
                isLoading={isProcessing}
              />
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={65} minSize={50} maxSize={80} className="h-full">
              <LivePreview
                files={generatedFiles}
                onRegenerateSection={handleRegenerateSection}
                isProcessing={isProcessing}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </main>
      </div>
    </ThemeProvider>
  );
};

export default ProjectEditor;
