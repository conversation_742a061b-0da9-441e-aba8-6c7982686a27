
import { AIModel, GenerationResult, StoredPromptHistory } from "../types";

const STORAGE_KEY = "code_generator_history";
const API_KEY_PREFIX = "code_generator_api_key_";
const DEFAULT_MODEL_KEY = "default_ai_model";

export function savePromptHistory(prompt: string, model: AIModel, result: GenerationResult): void {
  try {
    const history = getPromptHistory();
    const newEntry: StoredPromptHistory = {
      prompt,
      model,
      result,
      timestamp: Date.now()
    };
    
    // Keep only the 10 most recent entries
    history.unshift(newEntry);
    if (history.length > 10) history.length = 10;
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(history));
  } catch (error) {
    console.error("Failed to save to localStorage:", error);
  }
}

export function getPromptHistory(): StoredPromptHistory[] {
  try {
    const storedData = localStorage.getItem(STORAGE_KEY);
    if (!storedData) return [];
    return JSON.parse(storedData);
  } catch (error) {
    console.error("Failed to retrieve from localStorage:", error);
    return [];
  }
}

export function getLastPrompt(): StoredPromptHistory | null {
  const history = getPromptHistory();
  return history.length > 0 ? history[0] : null;
}

export function saveApiKey(model: AIModel, apiKey: string): void {
  try {
    localStorage.setItem(`${API_KEY_PREFIX}${model}`, apiKey);
  } catch (error) {
    console.error("Failed to save API key:", error);
  }
}

export function getApiKey(model: AIModel): string {
  try {
    return localStorage.getItem(`${API_KEY_PREFIX}${model}`) || "";
  } catch (error) {
    console.error("Failed to retrieve API key:", error);
    return "";
  }
}

export function clearApiKey(model: AIModel): void {
  try {
    localStorage.removeItem(`${API_KEY_PREFIX}${model}`);
  } catch (error) {
    console.error("Failed to clear API key:", error);
  }
}

export function saveDefaultModel(model: AIModel): void {
  try {
    localStorage.setItem(DEFAULT_MODEL_KEY, model);
  } catch (error) {
    console.error("Failed to save default model:", error);
  }
}

export function getDefaultModel(): AIModel | null {
  try {
    return localStorage.getItem(DEFAULT_MODEL_KEY) as AIModel | null;
  } catch (error) {
    console.error("Failed to retrieve default model:", error);
    return null;
  }
}
