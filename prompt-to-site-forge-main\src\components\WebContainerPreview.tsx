import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '@/components/ui/tooltip';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Toggle } from '@/components/ui/toggle';
import { 
  RefreshCw, 
  ExternalLink, 
  Maximize2, 
  Minimize2, 
  Monitor, 
  Tablet, 
  Smartphone, 
  Container,
  Info,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GeneratedFile } from '../types';
import { webcontainerService } from '../services/webcontainerService';

interface WebContainerPreviewProps {
  files: GeneratedFile[];
  onRegenerateSection?: (sectionId: string, prompt: string) => Promise<void>;
  isProcessing?: boolean;
  onError?: (error: string) => void;
}

type ViewportSize = 'desktop' | 'tablet' | 'mobile';

const WebContainerPreview: React.FC<WebContainerPreviewProps> = ({
  files,
  onRegenerateSection,
  isProcessing = false,
  onError
}) => {
  const [previewUrl, setPreviewUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [isFullScreen, setIsFullScreen] = useState<boolean>(false);
  const [viewportSize, setViewportSize] = useState<ViewportSize>('desktop');

  // Update WebContainer when files change
  useEffect(() => {
    if (files.length > 0) {
      // Add a small delay to ensure the component is fully mounted
      const timer = setTimeout(() => {
        updateWebContainer();
      }, 100);

      // Set up a fallback timeout to automatically switch to iframe preview if WebContainer fails
      const fallbackTimer = setTimeout(() => {
        if (isLoading && !previewUrl) {
          console.warn('⚠️ WebContainer taking too long, automatically falling back to iframe preview');
          setError('WebContainer failed to start. Automatically switching to iframe preview.');
          setIsLoading(false);
          // Trigger fallback to iframe preview
          onError?.('WebContainer timeout - falling back to iframe preview');
        }
      }, 10000); // 10 second timeout (reduced from 30)

      return () => {
        clearTimeout(timer);
        clearTimeout(fallbackTimer);
      };
    }
  }, [files, isLoading, previewUrl, onError]);

  const updateWebContainer = useCallback(async () => {
    if (files.length === 0) {
      console.log('🚫 No files to update WebContainer with');
      return;
    }

    console.log('🔄 Starting WebContainer update process...');
    setIsLoading(true);
    setError('');

    try {
      console.log('📋 Updating WebContainer with files:', files.map(f => ({
        name: f.name,
        type: f.type,
        contentLength: f.content?.length || 0,
        contentPreview: f.content?.substring(0, 100) + (f.content && f.content.length > 100 ? '...' : '')
      })));

      const url = await webcontainerService.updateFiles(files);

      // Validate the URL before setting it
      if (!url || typeof url !== 'string') {
        throw new Error('Invalid URL received from WebContainer service');
      }

      // Ensure URL is properly formatted
      const validUrl = url.startsWith('http') ? url : `https://${url}`;
      setPreviewUrl(validUrl);
      console.log('✅ WebContainer preview URL set:', validUrl);

      // Clear any previous errors on success
      setError('');

      // Add a small delay to ensure iframe loads properly
      setTimeout(() => {
        console.log('🔍 WebContainer iframe should be loading now');
      }, 500);

    } catch (err) {
      console.error('❌ Failed to update WebContainer:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load preview';

      // Enhanced error handling with more specific messages
      if (errorMessage.includes('cross-origin isolation') || errorMessage.includes('crossOriginIsolated')) {
        const fallbackMessage = 'WebContainer requires cross-origin isolation. Please ensure your server has the correct CORS headers. Falling back to iframe preview.';
        console.warn('⚠️ Cross-origin isolation error:', fallbackMessage);
        setError(fallbackMessage);
        onError?.(fallbackMessage);
      } else if (errorMessage.includes('timeout')) {
        const timeoutMessage = 'WebContainer server startup timed out. This may be due to system resources or network issues. Try refreshing or use iframe preview.';
        console.warn('⚠️ Timeout error:', timeoutMessage);
        setError(timeoutMessage);
        onError?.(timeoutMessage);
      } else if (errorMessage.includes('boot failed')) {
        const bootMessage = 'WebContainer failed to initialize. This may be due to browser compatibility or missing features. Try refreshing or use iframe preview.';
        console.warn('⚠️ Boot error:', bootMessage);
        setError(bootMessage);
        onError?.(bootMessage);
      } else {
        console.warn('⚠️ General error:', errorMessage);
        setError(errorMessage);
        onError?.(errorMessage);
      }
    } finally {
      setIsLoading(false);
      console.log('🏁 WebContainer update process completed');
    }
  }, [files, onError]);

  const refreshPreview = useCallback(() => {
    updateWebContainer();
  }, [updateWebContainer]);

  const debugWebContainer = useCallback(async () => {
    console.log('🔧 Starting WebContainer debug test...');
    try {
      const result = await webcontainerService.debugWebContainer();
      console.log('🔧 Debug result:', result);
      if (!result.success) {
        setError(`Debug failed: ${result.error}`);
      }
    } catch (error) {
      console.error('🔧 Debug test error:', error);
      setError(`Debug test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, []);

  const toggleFullScreen = useCallback(() => {
    setIsFullScreen(prev => !prev);
  }, []);

  const openInNewTab = useCallback(() => {
    if (previewUrl) {
      window.open(previewUrl, '_blank');
    }
  }, [previewUrl]);

  // Determine the appropriate width based on the selected viewport size
  const getPreviewWidth = () => {
    switch(viewportSize) {
      case 'mobile':
        return 'w-[375px]';
      case 'tablet':
        return 'w-[768px]';
      case 'desktop':
      default:
        return 'w-full';
    }
  };

  if (files.length === 0) {
    return (
      <Card className="flex flex-col h-full items-center justify-center bg-card/50 p-8 text-center">
        <div className="max-w-sm animate-fade-in">
          <Container className="h-16 w-16 text-muted-foreground mb-4 mx-auto opacity-70" />
          <p className="text-lg font-medium text-muted-foreground mb-2">No preview available</p>
          <p className="text-sm text-muted-foreground/80">
            Generate a website using the form above and your preview will appear here.
          </p>
        </div>
      </Card>
    );
  }

  return (
    <TooltipProvider>
      <div className={cn(
        "flex flex-col h-full border-l border-border/30 overflow-hidden transition-all duration-300 relative webcontainer-preview",
        isFullScreen ? "fixed inset-0 z-50 bg-background" : ""
      )}>
        <div className="bg-card/60 backdrop-blur-sm p-3 border-b border-border/30 flex items-center justify-between sticky top-0 z-10">
          <div className="flex items-center">
            <Container className="h-4 w-4 mr-2 text-blue-500" />
            <span className="text-sm font-medium text-foreground/90">
              WebContainer Preview
            </span>
            <Badge
              variant={previewUrl ? "default" : "secondary"}
              className={cn(
                "ml-2 text-xs",
                previewUrl ? "bg-green-500 hover:bg-green-600" : ""
              )}
            >
              {previewUrl ? "Live" : "Starting"}
            </Badge>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2">
                  <Info className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-2">
                  <p className="font-medium">Preview Mode: WebContainer</p>
                  <p className="text-xs">Running in a real Node.js environment with live server</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="flex items-center gap-2">
            {/* Responsive View Toggle */}
            <div className="flex items-center mr-2 p-1 bg-background/50 rounded-md border border-border/40">
              <Toggle 
                pressed={viewportSize === 'desktop'} 
                onPressedChange={() => setViewportSize('desktop')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Desktop view"
              >
                <Monitor className="h-4 w-4" />
                <span className="sr-only">Desktop</span>
              </Toggle>
              <Toggle 
                pressed={viewportSize === 'tablet'} 
                onPressedChange={() => setViewportSize('tablet')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Tablet view"
              >
                <Tablet className="h-4 w-4" />
                <span className="sr-only">Tablet</span>
              </Toggle>
              <Toggle 
                pressed={viewportSize === 'mobile'} 
                onPressedChange={() => setViewportSize('mobile')}
                size="sm" 
                className="h-8 w-8 p-0 data-[state=on]:bg-primary/20"
                title="Mobile view"
              >
                <Smartphone className="h-4 w-4" />
                <span className="sr-only">Mobile</span>
              </Toggle>
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={refreshPreview}
              title="Refresh preview"
              className="h-8 w-8 p-0 border-border/40"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              <span className="sr-only">Refresh</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={debugWebContainer}
              title="Debug WebContainer"
              className="h-8 w-8 p-0 border-border/40"
            >
              <span className="text-xs">🔧</span>
              <span className="sr-only">Debug</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const debugInfo = {
                  previewUrl,
                  isLoading,
                  error,
                  filesCount: files.length,
                  viewportSize,
                  isFullScreen,
                  crossOriginIsolated: typeof window !== 'undefined' ? window.crossOriginIsolated : 'N/A',
                  location: typeof window !== 'undefined' ? window.location.href : 'N/A',
                  userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'N/A'
                };
                console.log('🔍 Preview Debug Info:', debugInfo);

                // Test if the iframe URL is accessible
                if (previewUrl) {
                  console.log('🔍 Testing preview URL accessibility...');
                  fetch(previewUrl, { mode: 'no-cors' })
                    .then(() => console.log('✅ Preview URL is accessible'))
                    .catch(err => console.log('❌ Preview URL access failed:', err));
                }

                alert(`Preview URL: ${previewUrl}\nFiles: ${files.length}\nViewport: ${viewportSize}\nError: ${error || 'None'}\nCross-Origin Isolated: ${debugInfo.crossOriginIsolated}`);
              }}
              title="Debug Preview Display"
              className="h-8 w-8 p-0 border-border/40"
            >
              <span className="text-xs">📊</span>
              <span className="sr-only">Debug Display</span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={toggleFullScreen} 
              title={isFullScreen ? "Exit fullscreen" : "Fullscreen preview"} 
              className="h-8 w-8 p-0 border-border/40"
            >
              {isFullScreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              <span className="sr-only">Fullscreen</span>
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={openInNewTab} 
              title="Open in new tab" 
              className="h-8 w-8 p-0 border-border/40"
              disabled={!previewUrl}
            >
              <ExternalLink className="h-4 w-4" />
              <span className="sr-only">Open in new tab</span>
            </Button>
          </div>
        </div>
        
        <div className="flex-1 w-full h-full overflow-hidden">
          <div className={cn(
            "w-full h-full bg-white dark:bg-slate-900 flex justify-center items-stretch",
            isFullScreen ? "h-[calc(100vh-60px)]" : "h-full"
          )}>
            {error ? (
              <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <AlertCircle className="h-16 w-16 text-red-500 mb-4 opacity-70" />
                <p className="text-lg font-medium text-red-600 mb-2">Preview Error</p>
                <p className="text-sm text-muted-foreground max-w-md">{error}</p>
                <div className="flex gap-2 mt-4">
                  <Button
                    onClick={refreshPreview}
                    variant="outline"
                    disabled={isLoading}
                  >
                    Try Again
                  </Button>
                  <Button
                    onClick={debugWebContainer}
                    variant="secondary"
                    disabled={isLoading}
                  >
                    🔧 Debug
                  </Button>
                </div>
              </div>
            ) : isLoading ? (
              <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <Loader2 className="h-16 w-16 text-blue-500 mb-4 animate-spin" />
                <p className="text-lg font-medium text-foreground mb-2">Starting WebContainer...</p>
                <p className="text-sm text-muted-foreground mb-4">This may take a moment</p>
                <div className="flex gap-2 mt-4">
                  <Button
                    onClick={() => {
                      console.log('🔄 User requested WebContainer refresh');
                      refreshPreview();
                    }}
                    variant="outline"
                    size="sm"
                  >
                    Retry
                  </Button>
                  <Button
                    onClick={() => {
                      console.log('👤 User manually switching to iframe preview');
                      setError('Switched to iframe preview');
                      setIsLoading(false);
                      onError?.('User switched to iframe preview');
                    }}
                    variant="secondary"
                    size="sm"
                  >
                    Use Iframe Preview
                  </Button>
                </div>
              </div>
            ) : previewUrl ? (
              <div className={cn(
                "transition-all duration-300 h-full preview-iframe-container",
                getPreviewWidth(),
                viewportSize !== 'desktop' && "border-x border-border/20 shadow-sm"
              )}>
                <iframe
                  src={previewUrl}
                  title="WebContainer Preview"
                  className="preview-iframe w-full h-full border-0"
                  style={{
                    width: '100%',
                    height: '100%',
                    minHeight: '100%',
                    display: 'block'
                  }}
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads"
                  onLoad={() => {
                    console.log('🎯 WebContainer iframe loaded successfully');
                    // Clear any loading state after successful load
                    setIsLoading(false);
                    setError('');
                  }}
                  onError={(e) => {
                    console.error('❌ WebContainer iframe error:', e);
                    const errorMsg = 'Failed to load WebContainer preview. The server may not be ready yet.';
                    setError(errorMsg);
                    // Auto-retry after a delay
                    setTimeout(() => {
                      if (previewUrl) {
                        console.log('🔄 Auto-retrying WebContainer preview...');
                        refreshPreview();
                      }
                    }, 3000);
                  }}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                <Container className="h-16 w-16 text-muted-foreground mb-4 opacity-70" />
                <p className="text-lg font-medium text-muted-foreground mb-2">Initializing Preview</p>
                <p className="text-sm text-muted-foreground">Setting up WebContainer environment...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default WebContainerPreview;
