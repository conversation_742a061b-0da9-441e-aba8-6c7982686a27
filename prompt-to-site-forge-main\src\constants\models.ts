
import { ModelOption } from "../types";

export const MODEL_OPTIONS: ModelOption[] = [
  {
    id: 'openai',
    name: 'OpenAI (GPT-4)',
    description: 'Advanced AI model for code generation with strong coding capabilities',
    apiKeyPlaceholder: 'sk-...',
  },
  {
    id: 'google',
    name: 'Google Gemini',
    description: 'Google\'s multimodal AI with strong coding abilities',
    apiKeyPlaceholder: 'AI...',
  },
  {
    id: 'anthropic',
    name: 'Anthropic <PERSON>',
    description: 'AI assistant designed for safety and helpfulness',
    apiKeyPlaceholder: 'sk-ant-...',
  },
  {
    id: 'mistral',
    name: 'Mistral',
    description: 'Open-weight language model with strong capabilities',
    apiKeyPlaceholder: 'mi-...',
  },
];

export const EXAMPLE_PROMPTS = [
  "Create a modern portfolio website with a hero section, about me, skills, and contact form",
  "Generate a landing page for a SaaS product with pricing table and testimonials",
  "Build a responsive blog homepage with featured posts and newsletter signup",
  "Create an e-commerce product page with image gallery and add to cart functionality"
];
