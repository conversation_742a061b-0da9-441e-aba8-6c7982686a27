<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebContainer Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>WebContainer Functionality Test</h1>
    
    <div class="test-section">
        <h2>Environment Check</h2>
        <div id="env-status"></div>
        <button onclick="checkEnvironment()">Check Environment</button>
    </div>

    <div class="test-section">
        <h2>Cross-Origin Isolation</h2>
        <div id="cors-status"></div>
        <button onclick="checkCORS()">Check CORS Headers</button>
    </div>

    <div class="test-section">
        <h2>WebContainer API</h2>
        <div id="webcontainer-status"></div>
        <button onclick="testWebContainer()">Test WebContainer</button>
    </div>

    <div class="test-section">
        <h2>Console Output</h2>
        <pre id="console-output"></pre>
        <button onclick="clearConsole()">Clear Console</button>
    </div>

    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        let consoleOutput = [];
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateConsoleDisplay();
        }
        
        console.log = (...args) => {
            originalLog(...args);
            addToConsole('log', ...args);
        };
        
        console.error = (...args) => {
            originalError(...args);
            addToConsole('error', ...args);
        };
        
        console.warn = (...args) => {
            originalWarn(...args);
            addToConsole('warn', ...args);
        };
        
        function updateConsoleDisplay() {
            document.getElementById('console-output').textContent = consoleOutput.slice(-20).join('\n');
        }
        
        function clearConsole() {
            consoleOutput = [];
            updateConsoleDisplay();
        }
        
        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function checkEnvironment() {
            console.log('🔍 Checking environment...');
            
            const checks = {
                'Window object': typeof window !== 'undefined',
                'Location': window.location ? window.location.href : 'N/A',
                'User Agent': navigator.userAgent,
                'Protocol': window.location.protocol,
                'Host': window.location.host,
                'SharedArrayBuffer': typeof SharedArrayBuffer !== 'undefined',
                'Worker': typeof Worker !== 'undefined',
                'WebAssembly': typeof WebAssembly !== 'undefined'
            };
            
            let html = '<h3>Environment Details:</h3>';
            for (const [key, value] of Object.entries(checks)) {
                html += `<p><strong>${key}:</strong> ${value}</p>`;
            }
            
            setStatus('env-status', html, 'info');
        }
        
        function checkCORS() {
            console.log('🔍 Checking cross-origin isolation...');
            
            const crossOriginIsolated = window.crossOriginIsolated;
            const headers = {
                'Cross-Origin-Embedder-Policy': 'Unknown',
                'Cross-Origin-Opener-Policy': 'Unknown'
            };
            
            // Try to detect headers (limited in browser)
            let html = `<h3>Cross-Origin Isolation Status:</h3>`;
            html += `<p><strong>crossOriginIsolated:</strong> ${crossOriginIsolated}</p>`;
            html += `<p><strong>SharedArrayBuffer available:</strong> ${typeof SharedArrayBuffer !== 'undefined'}</p>`;
            
            if (crossOriginIsolated) {
                setStatus('cors-status', html + '<p class="success">✅ Cross-origin isolation is enabled!</p>', 'success');
            } else {
                setStatus('cors-status', html + '<p class="error">❌ Cross-origin isolation is NOT enabled. WebContainer will not work.</p>', 'error');
            }
        }
        
        async function testWebContainer() {
            console.log('🔍 Testing WebContainer...');
            setStatus('webcontainer-status', 'Testing WebContainer...', 'info');
            
            try {
                // Check if WebContainer API is available
                if (typeof window.WebContainer === 'undefined') {
                    // Try to import it
                    const { WebContainer } = await import('@webcontainer/api');
                    window.WebContainer = WebContainer;
                }
                
                if (!window.crossOriginIsolated) {
                    throw new Error('Cross-origin isolation is required for WebContainer');
                }
                
                console.log('🚀 Attempting to boot WebContainer...');
                const webcontainer = await window.WebContainer.boot();
                console.log('✅ WebContainer booted successfully!');
                
                // Test file system
                await webcontainer.mount({
                    'test.txt': {
                        file: {
                            contents: 'Hello from WebContainer test!'
                        }
                    }
                });
                console.log('✅ File system test successful!');
                
                setStatus('webcontainer-status', '✅ WebContainer is working correctly!', 'success');
                
            } catch (error) {
                console.error('❌ WebContainer test failed:', error);
                setStatus('webcontainer-status', `❌ WebContainer test failed: ${error.message}`, 'error');
            }
        }
        
        // Auto-run basic checks on load
        window.addEventListener('load', () => {
            console.log('🔍 Page loaded, running initial checks...');
            checkEnvironment();
            checkCORS();
        });
    </script>
</body>
</html>
