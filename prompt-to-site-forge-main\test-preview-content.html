<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview Content Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .test-iframe {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 Preview Content Fix Test</h1>
    
    <div class="test-section">
        <h2>Issues Fixed</h2>
        <div class="status success">
            <strong>✅ Fixed:</strong> Removed external CSS/JS file references that caused 404 errors
        </div>
        <div class="status success">
            <strong>✅ Fixed:</strong> Added default sections for all website types
        </div>
        <div class="status success">
            <strong>✅ Fixed:</strong> Improved iframe content loading with error handling
        </div>
        <div class="status success">
            <strong>✅ Fixed:</strong> Suppressed document replacement warnings
        </div>
    </div>

    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>To test the fixes:</p>
        <ol>
            <li>Go to the main application: <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
            <li>Enter a simple prompt like: <strong>"Create a modern portfolio"</strong></li>
            <li>Click "Generate Website"</li>
            <li>Check that the preview shows:</li>
            <ul>
                <li>Header with navigation</li>
                <li>Hero section</li>
                <li>About section</li>
                <li>Gallery/Services section</li>
                <li>Contact form</li>
                <li>Footer</li>
            </ul>
            <li>Check browser console for errors (should be minimal)</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Generated HTML Structure</h2>
        <p>The generated website should now include these sections by default:</p>
        <div class="code-block">
&lt;!DOCTYPE html&gt;
&lt;html lang="en"&gt;
&lt;head&gt;
    &lt;meta charset="UTF-8"&gt;
    &lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
    &lt;title&gt;Portfolio Website&lt;/title&gt;
    &lt;!-- No external CSS/JS references --&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;header class="header"&gt;
        &lt;nav class="navbar"&gt;
            &lt;!-- Navigation menu --&gt;
        &lt;/nav&gt;
    &lt;/header&gt;

    &lt;main&gt;
        &lt;section class="hero" data-section-id="hero"&gt;
            &lt;!-- Hero content --&gt;
        &lt;/section&gt;

        &lt;section class="about" data-section-id="about"&gt;
            &lt;!-- About section content --&gt;
        &lt;/section&gt;

        &lt;section class="gallery" data-section-id="gallery"&gt;
            &lt;!-- Gallery/Services content --&gt;
        &lt;/section&gt;

        &lt;section class="contact" data-section-id="contact"&gt;
            &lt;!-- Contact form --&gt;
        &lt;/section&gt;
    &lt;/main&gt;

    &lt;footer class="footer"&gt;
        &lt;!-- Footer content --&gt;
    &lt;/footer&gt;
&lt;/body&gt;
&lt;/html&gt;
        </div>
    </div>

    <div class="test-section">
        <h2>Sample Complete Website Preview</h2>
        <p>This is how the fixed website should look (with all sections visible):</p>
        <iframe class="test-iframe" src="data:text/html,
        <!DOCTYPE html>
        <html>
        <head>
            <title>Portfolio Website</title>
            <style>
                body { margin: 0; font-family: Arial, sans-serif; }
                .header { background: #333; color: white; padding: 20px; text-align: center; }
                .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 20px; text-align: center; }
                .content { padding: 60px 20px; max-width: 1200px; margin: 0 auto; }
                .section { margin: 40px 0; padding: 40px; background: #f8f9fa; border-radius: 8px; }
                .footer { background: #333; color: white; padding: 40px 20px; text-align: center; }
                h1 { font-size: 2.5rem; margin-bottom: 1rem; }
                h2 { color: #333; margin-bottom: 1rem; }
                p { line-height: 1.6; margin-bottom: 1rem; }
                .nav-menu { list-style: none; display: flex; gap: 20px; justify-content: center; margin: 0; padding: 0; }
                .nav-menu a { color: white; text-decoration: none; }
            </style>
        </head>
        <body>
            <header class='header'>
                <nav>
                    <h2>Portfolio Website</h2>
                    <ul class='nav-menu'>
                        <li><a href='#home'>Home</a></li>
                        <li><a href='#about'>About</a></li>
                        <li><a href='#services'>Services</a></li>
                        <li><a href='#contact'>Contact</a></li>
                    </ul>
                </nav>
            </header>
            
            <section class='hero'>
                <h1>Welcome to My Portfolio</h1>
                <p>Modern. Creative. Professional.</p>
            </section>
            
            <main class='content'>
                <section class='section'>
                    <h2>About Me</h2>
                    <p>This is the about section with information about the person or business.</p>
                </section>
                
                <section class='section'>
                    <h2>My Work</h2>
                    <p>This section showcases the portfolio or services offered.</p>
                </section>
                
                <section class='section'>
                    <h2>Contact Me</h2>
                    <p>Get in touch for collaborations and projects.</p>
                </section>
            </main>
            
            <footer class='footer'>
                <p>&copy; 2024 Portfolio Website. All rights reserved.</p>
            </footer>
        </body>
        </html>
        "></iframe>
    </div>

    <div class="test-section">
        <h2>Console Error Check</h2>
        <p>After generating a website, check the browser console. You should see:</p>
        <div class="status success">
            <strong>✅ No 404 errors</strong> for styles.css or script.js
        </div>
        <div class="status success">
            <strong>✅ Minimal warnings</strong> (document replacement warnings should be suppressed)
        </div>
        <div class="status info">
            <strong>ℹ️ Expected logs:</strong> "Website initialized successfully!" and content length logs
        </div>
    </div>

    <script>
        // Test function to verify the fixes
        function testGeneratedContent() {
            console.log('Testing generated content structure...');
            
            // This would be called after generating a website
            const expectedSections = ['hero', 'about', 'services', 'contact'];
            console.log('Expected sections:', expectedSections);
            
            // In the actual app, this would check the generated HTML
            console.log('✅ Test setup complete - use the main app to verify fixes');
        }
        
        // Run test on page load
        testGeneratedContent();
    </script>
</body>
</html>
