
export type AIModel = 'openai' | 'google' | 'anthropic' | 'mistral';

export interface ModelOption {
  id: AIModel;
  name: string;
  description: string;
  apiKeyPlaceholder: string;
}

export interface GeneratedFile {
  id: string;
  name: string;
  language: string;
  content: string;
}

export interface GenerationResult {
  files: GeneratedFile[];
  timestamp: number;
}

export interface StoredPromptHistory {
  prompt: string;
  model: AIModel;
  result: GenerationResult;
  timestamp: number;
}
