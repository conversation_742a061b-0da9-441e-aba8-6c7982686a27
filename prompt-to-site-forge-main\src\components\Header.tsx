
import React from 'react';
import { useTheme } from '../context/ThemeContext';
import { Button } from '@/components/ui/button';
import { Moon, Sun, Settings as SettingsIcon, Github } from 'lucide-react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';

const Header: React.FC = () => {
  const { theme, toggleTheme } = useTheme();

  return (
    <header className="sticky top-0 z-10 border-b border-border/40 bg-background/80 backdrop-blur-md">
      <div className="container flex items-center justify-between h-16 px-6">
        <Link to="/" className="flex items-center space-x-2 group">
          <span className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-400 transition-all">
            WebGen
          </span>
          <span className="hidden sm:inline-flex text-sm font-semibold px-3 py-1.5 bg-primary/10 rounded-md text-primary transition-all group-hover:bg-primary/20">
            AI Website Generator
          </span>
        </Link>
        
        <div className="flex items-center space-x-3">
          <Link to="/settings">
            <Button
              variant="ghost"
              size="icon"
              title="Settings"
              className="hover:bg-primary/5 transition-colors relative"
            >
              <SettingsIcon className="h-5 w-5" />
              <span className="sr-only">Settings</span>
            </Button>
          </Link>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleTheme}
            className="hover:bg-primary/5 transition-colors"
            aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
          >
            {theme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
            <span className="sr-only">Toggle theme</span>
          </Button>
          
          <a 
            href="https://github.com/yourusername/webgen" 
            target="_blank"
            rel="noopener noreferrer"
            className={cn(
              "hidden sm:inline-flex items-center gap-1.5 px-3 py-1.5 rounded-md text-sm font-medium transition-colors",
              "text-muted-foreground hover:text-foreground hover:bg-primary/5"
            )}
          >
            <Github className="h-4 w-4" />
            GitHub
          </a>
        </div>
      </div>
    </header>
  );
};

export default Header;
