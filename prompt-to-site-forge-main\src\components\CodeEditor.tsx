
import React, { useEffect, useRef, useState } from 'react';
import Editor, { Monaco, OnMount } from '@monaco-editor/react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { GeneratedFile } from '../types';
import { downloadAsZip } from '../utils/fileUtils';
import { Copy, Download, FileCode } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

interface CodeEditorProps {
  files: GeneratedFile[];
}

const CodeEditor: React.FC<CodeEditorProps> = ({ files }) => {
  const [activeFileId, setActiveFileId] = useState<string>('');
  const [isEditorReady, setIsEditorReady] = useState(false);
  const { toast } = useToast();
  const monacoRef = useRef<Monaco | null>(null);
  const editorRef = useRef<any>(null);

  // Set the first file as active by default
  useEffect(() => {
    if (files.length > 0 && (!activeFileId || !files.find(f => f.id === activeFileId))) {
      setActiveFileId(files[0].id);
    }
  }, [files, activeFileId]);

  const handleEditorDidMount: OnMount = (editor, monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;
    
    // Set editor options for better visibility
    monaco.editor.defineTheme('webgenDark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#191A23',
        'editor.lineHighlightBackground': '#2B2D3A',
      }
    });
    
    monaco.editor.defineTheme('webgenLight', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#F8FAFC',
        'editor.lineHighlightBackground': '#E2E8F0',
      }
    });
    
    // Apply theme based on current system theme
    const isDark = document.documentElement.classList.contains('dark');
    monaco.editor.setTheme(isDark ? 'webgenDark' : 'webgenLight');
    
    setIsEditorReady(true);
  };

  const copyToClipboard = async () => {
    const activeFile = files.find(file => file.id === activeFileId);
    if (activeFile && activeFile.content) {
      try {
        await navigator.clipboard.writeText(activeFile.content);
        toast({
          title: "Copied!",
          description: `${activeFile.name} copied to clipboard`,
        });
      } catch (error) {
        toast({
          variant: "destructive",
          title: "Copy failed",
          description: "Failed to copy code to clipboard",
        });
      }
    }
  };

  const handleDownload = () => {
    if (files.length > 0) {
      downloadAsZip(files);
      toast({
        title: "Downloaded!",
        description: "Your website files have been downloaded as a ZIP file",
      });
    }
  };

  if (files.length === 0) {
    return (
      <Card className="flex flex-col space-y-4 h-full items-center justify-center bg-card/50 p-8">
        <FileCode className="h-16 w-16 text-muted-foreground/50" />
        <div className="text-center">
          <p className="text-lg font-medium text-muted-foreground mb-2">No files generated yet</p>
          <p className="text-sm text-muted-foreground/70">
            Enter a prompt and click "Generate Website" to see your code here
          </p>
        </div>
      </Card>
    );
  }

  const activeFile = files.find(file => file.id === activeFileId);

  return (
    <div className="flex flex-col h-full overflow-hidden">
      <div className="flex justify-between items-center p-3 border-b border-border/30 bg-card/60 backdrop-blur-sm">
        <div className="flex-1">
          <Tabs value={activeFileId} onValueChange={setActiveFileId} className="w-full">
            <div className="flex justify-between items-center">
              <TabsList className="bg-background/50 h-8 p-1">
                {files.map(file => (
                  <TabsTrigger 
                    key={file.id} 
                    value={file.id}
                    className="text-xs h-7 px-3 rounded-sm"
                  >
                    {file.name}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              <div className="flex space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={copyToClipboard}
                  disabled={!isEditorReady || !activeFile?.content}
                  title="Copy code to clipboard"
                  className="h-8 w-8 p-0 border-border/40"
                >
                  <Copy className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={handleDownload}
                  title="Download as ZIP"
                  className="h-8 w-8 p-0 border-border/40"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </Tabs>
        </div>
      </div>
      
      <div className="flex-1 overflow-hidden">
        {!isEditorReady ? (
          <div className="p-4 h-full">
            <Skeleton className="w-full h-full" />
          </div>
        ) : (
          activeFile && (
            <Editor
              height="100%"
              language={activeFile.language}
              value={activeFile.content || '// No content available'}
              options={{
                readOnly: true,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                fontSize: 14,
                fontFamily: "'JetBrains Mono', monospace",
                wordWrap: 'on',
                automaticLayout: true,
                lineNumbers: 'on',
                renderLineHighlight: 'all',
                padding: { top: 16, bottom: 16 },
                scrollbar: {
                  vertical: 'visible',
                  horizontal: 'visible',
                  useShadows: true,
                  verticalHasArrows: false,
                  horizontalHasArrows: false,
                  verticalScrollbarSize: 12,
                  horizontalScrollbarSize: 12,
                }
              }}
              onMount={handleEditorDidMount}
              className="border rounded-md overflow-hidden"
            />
          )
        )}
      </div>
    </div>
  );
};

export default CodeEditor;
