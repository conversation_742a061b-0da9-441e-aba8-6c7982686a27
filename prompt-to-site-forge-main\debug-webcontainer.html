<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebContainer Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        #log {
            background: #000;
            color: #0f0;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebContainer Debug Test</h1>
    
    <div class="debug-section">
        <h2>Environment Check</h2>
        <div id="env-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>WebContainer Test</h2>
        <button onclick="testWebContainer()" id="test-btn">Test WebContainer</button>
        <div id="webcontainer-status"></div>
    </div>
    
    <div class="debug-section">
        <h2>Debug Log</h2>
        <div id="log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script type="module">
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Environment check
        function checkEnvironment() {
            log('🔍 Checking environment...');
            
            const checks = {
                'Cross-Origin Isolation': window.crossOriginIsolated,
                'SharedArrayBuffer': typeof SharedArrayBuffer !== 'undefined',
                'WebAssembly': typeof WebAssembly !== 'undefined',
                'Worker': typeof Worker !== 'undefined',
                'Location': window.location.href,
                'User Agent': navigator.userAgent.substring(0, 100) + '...'
            };

            let envHtml = '';
            let allGood = true;

            for (const [check, result] of Object.entries(checks)) {
                const status = result ? '✅' : '❌';
                const type = result ? 'success' : 'error';
                envHtml += `<div class="status ${type}">${status} ${check}: ${result}</div>`;
                
                if (check === 'Cross-Origin Isolation' && !result) {
                    allGood = false;
                }
                
                log(`${status} ${check}: ${result}`);
            }

            document.getElementById('env-status').innerHTML = envHtml;
            
            if (!allGood) {
                log('❌ Environment check failed - WebContainer may not work properly');
                setStatus('webcontainer-status', 'Environment not suitable for WebContainer', 'error');
            } else {
                log('✅ Environment check passed');
            }
        }

        // WebContainer test
        window.testWebContainer = async function() {
            const button = document.getElementById('test-btn');
            button.disabled = true;
            button.textContent = 'Testing...';
            
            log('🚀 Starting WebContainer test...');
            setStatus('webcontainer-status', 'Testing WebContainer...', 'info');

            try {
                // Import WebContainer API
                log('📦 Importing WebContainer API...');
                const { WebContainer } = await import('@webcontainer/api');
                log('✅ WebContainer API imported successfully');

                // Boot WebContainer
                log('🔄 Booting WebContainer...');
                const webcontainer = await WebContainer.boot();
                log('✅ WebContainer booted successfully');

                // Test file system
                log('📁 Testing file system...');
                await webcontainer.mount({
                    'test.html': {
                        file: {
                            contents: `<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body>
    <h1>WebContainer Test Page</h1>
    <p>If you can see this, WebContainer is working!</p>
    <script>console.log('WebContainer test page loaded');</script>
</body>
</html>`
                        }
                    },
                    'package.json': {
                        file: {
                            contents: JSON.stringify({
                                name: 'test-app',
                                version: '1.0.0',
                                scripts: { start: 'node server.js' },
                                dependencies: {}
                            }, null, 2)
                        }
                    },
                    'server.js': {
                        file: {
                            contents: `const http = require('http');
const fs = require('fs');
const server = http.createServer((req, res) => {
    if (req.url === '/' || req.url === '/test.html') {
        fs.readFile('test.html', (err, data) => {
            if (err) {
                res.writeHead(500);
                res.end('Error loading file');
            } else {
                res.writeHead(200, { 'Content-Type': 'text/html' });
                res.end(data);
            }
        });
    } else {
        res.writeHead(404);
        res.end('Not found');
    }
});
server.listen(3000, () => console.log('Server running on port 3000'));`
                        }
                    }
                });
                log('✅ File system mounted successfully');

                // Start server
                log('🚀 Starting test server...');
                const process = await webcontainer.spawn('node', ['server.js']);
                
                // Wait for server to be ready
                const url = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('Server startup timeout'));
                    }, 10000);

                    webcontainer.on('server-ready', (port, url) => {
                        if (port === 3000) {
                            clearTimeout(timeout);
                            resolve(url);
                        }
                    });

                    process.output.pipeTo(new WritableStream({
                        write(data) {
                            log(`📡 Server output: ${data}`);
                            if (data.includes('Server running on port 3000')) {
                                clearTimeout(timeout);
                                resolve('http://localhost:3000'); // Fallback URL
                            }
                        }
                    }));
                });

                log(`✅ WebContainer test server ready at: ${url}`);
                setStatus('webcontainer-status', `WebContainer working! Server at: ${url}`, 'success');

                // Create test iframe
                const iframe = document.createElement('iframe');
                iframe.src = url;
                iframe.style.width = '100%';
                iframe.style.height = '300px';
                iframe.style.border = '1px solid #ddd';
                iframe.style.marginTop = '10px';
                
                const statusDiv = document.getElementById('webcontainer-status');
                statusDiv.appendChild(iframe);

            } catch (error) {
                log(`❌ WebContainer test failed: ${error.message}`);
                setStatus('webcontainer-status', `WebContainer test failed: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Test WebContainer';
            }
        };

        // Run environment check on load
        checkEnvironment();
    </script>
</body>
</html>
