import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Filter out browser extension errors that don't affect our application
const originalConsoleError = console.error;
console.error = (...args) => {
  const message = args.join(' ');
  const isExtensionError = [
    'ChromeRuntimeError',
    'runtime.lastError',
    'Could not establish connection',
    'Receiving end does not exist',
    'Extension context invalidated',
    'chrome-extension://'
  ].some(keyword => message.includes(keyword));

  if (!isExtensionError) {
    originalConsoleError.apply(console, args);
  }
};

// Filter out extension warnings too
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  const message = args.join(' ');
  const isExtensionWarning = [
    'ChromeRuntimeError',
    'runtime.lastError',
    'Extension context'
  ].some(keyword => message.includes(keyword));

  if (!isExtensionWarning) {
    originalConsoleWarn.apply(console, args);
  }
};

createRoot(document.getElementById("root")!).render(<App />);
