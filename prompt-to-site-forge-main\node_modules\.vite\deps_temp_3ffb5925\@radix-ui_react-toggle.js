"use client";
import {
  useControllableState
} from "./chunk-D44RF2N3.js";
import {
  composeEventHandlers
} from "./chunk-P7QW2XMJ.js";
import {
  Primitive
} from "./chunk-DI4K3IQB.js";
import "./chunk-CYR3URII.js";
import "./chunk-YRFWYLDN.js";
import {
  require_jsx_runtime
} from "./chunk-PXMHN2KN.js";
import {
  require_react
} from "./chunk-OIM7OLJW.js";
import {
  __toESM
} from "./chunk-RDKGUBC5.js";

// node_modules/@radix-ui/react-toggle/dist/index.mjs
var React = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NAME = "Toggle";
var Toggle = React.forwardRef((props, forwardedRef) => {
  const { pressed: pressedProp, defaultPressed, onPressedChange, ...buttonProps } = props;
  const [pressed, setPressed] = useControllableState({
    prop: pressedProp,
    onChange: onPressedChange,
    defaultProp: defaultPressed ?? false,
    caller: NAME
  });
  return (0, import_jsx_runtime.jsx)(
    Primitive.button,
    {
      type: "button",
      "aria-pressed": pressed,
      "data-state": pressed ? "on" : "off",
      "data-disabled": props.disabled ? "" : void 0,
      ...buttonProps,
      ref: forwardedRef,
      onClick: composeEventHandlers(props.onClick, () => {
        if (!props.disabled) {
          setPressed(!pressed);
        }
      })
    }
  );
});
Toggle.displayName = NAME;
var Root = Toggle;
export {
  Root,
  Toggle
};
//# sourceMappingURL=@radix-ui_react-toggle.js.map
