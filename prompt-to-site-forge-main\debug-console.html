<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Console - WebContainer Issues</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            margin: 0;
            padding: 20px;
            line-height: 1.4;
        }
        .header {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #00ff00;
        }
        .section {
            background: #2a2a2a;
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .error { color: #ff4444; border-left: 4px solid #ff4444; }
        .warning { color: #ffaa00; border-left: 4px solid #ffaa00; }
        .success { color: #44ff44; border-left: 4px solid #44ff44; }
        .info { color: #4488ff; border-left: 4px solid #4488ff; }
        button {
            background: #444;
            color: #00ff00;
            border: 1px solid #666;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-family: inherit;
        }
        button:hover { background: #555; }
        .console-output {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .filter-buttons {
            margin: 10px 0;
        }
        .filter-buttons button {
            font-size: 11px;
            padding: 4px 8px;
        }
        .filter-buttons button.active {
            background: #00ff00;
            color: #000;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 WebContainer Debug Console</h1>
        <p>Diagnostic tool to identify and resolve browser extension errors vs. application issues</p>
    </div>

    <div class="section info">
        <h2>📊 Error Analysis</h2>
        <p><strong>ChromeRuntimeError:</strong> Browser extension communication error (NOT your app)</p>
        <p><strong>"Could not establish connection":</strong> Extension trying to connect to non-existent endpoint</p>
        <p><strong>Solution:</strong> These errors are harmless and don't affect WebContainer functionality</p>
    </div>

    <div class="section">
        <h2>🧪 Quick Tests</h2>
        <button onclick="testWebContainer()">Test WebContainer API</button>
        <button onclick="testCrossOrigin()">Test Cross-Origin Isolation</button>
        <button onclick="testApplication()">Test Main Application</button>
        <button onclick="clearConsole()">Clear Console</button>
        <button onclick="filterErrors()">Filter Real Errors</button>
    </div>

    <div class="section">
        <h2>📱 Console Filter</h2>
        <div class="filter-buttons">
            <button onclick="showAll()" class="active" id="show-all">Show All</button>
            <button onclick="showErrors()" id="show-errors">Errors Only</button>
            <button onclick="showWarnings()" id="show-warnings">Warnings Only</button>
            <button onclick="showAppLogs()" id="show-app">App Logs Only</button>
            <button onclick="hideExtensions()" id="hide-ext">Hide Extensions</button>
        </div>
        <div class="console-output" id="console-output">
            Console output will appear here...
        </div>
    </div>

    <div class="section success">
        <h2>✅ How to Fix Extension Errors</h2>
        <ol>
            <li><strong>Disable Extensions:</strong> Try opening in incognito mode</li>
            <li><strong>Filter Console:</strong> Use "Hide Extensions" button above</li>
            <li><strong>Focus on App Errors:</strong> Look for WebContainer-specific errors only</li>
            <li><strong>Ignore Runtime Errors:</strong> ChromeRuntimeError is not your app's fault</li>
        </ol>
    </div>

    <div class="section warning">
        <h2>⚠️ Real Issues to Watch For</h2>
        <ul>
            <li>Cross-origin isolation errors</li>
            <li>WebContainer boot failures</li>
            <li>File system mount errors</li>
            <li>Server startup timeouts</li>
            <li>Network connection issues</li>
        </ul>
    </div>

    <div class="section">
        <h2>🚀 Test Your Application</h2>
        <p>1. <a href="http://localhost:8082" target="_blank" style="color: #4488ff;">Open Main Application</a></p>
        <p>2. Generate a website with prompt: "Create a modern portfolio website"</p>
        <p>3. Check if preview shows full content (not just header)</p>
        <p>4. Test viewport controls (desktop/tablet/mobile)</p>
        <p>5. Switch between WebContainer and iframe modes</p>
    </div>

    <script>
        let originalConsole = {};
        let consoleMessages = [];
        let currentFilter = 'all';

        // Capture console messages
        function setupConsoleCapture() {
            ['log', 'error', 'warn', 'info'].forEach(method => {
                originalConsole[method] = console[method];
                console[method] = function(...args) {
                    const message = {
                        type: method,
                        content: args.join(' '),
                        timestamp: new Date().toLocaleTimeString(),
                        isExtension: isExtensionError(args.join(' '))
                    };
                    consoleMessages.push(message);
                    updateConsoleDisplay();
                    originalConsole[method].apply(console, args);
                };
            });
        }

        function isExtensionError(message) {
            const extensionKeywords = [
                'ChromeRuntimeError',
                'runtime.lastError',
                'Could not establish connection',
                'Receiving end does not exist',
                'Extension context invalidated',
                'chrome-extension://'
            ];
            return extensionKeywords.some(keyword => message.includes(keyword));
        }

        function updateConsoleDisplay() {
            const output = document.getElementById('console-output');
            let filteredMessages = consoleMessages;

            switch(currentFilter) {
                case 'errors':
                    filteredMessages = consoleMessages.filter(m => m.type === 'error');
                    break;
                case 'warnings':
                    filteredMessages = consoleMessages.filter(m => m.type === 'warn');
                    break;
                case 'app':
                    filteredMessages = consoleMessages.filter(m => !m.isExtension);
                    break;
                case 'hide-ext':
                    filteredMessages = consoleMessages.filter(m => !m.isExtension);
                    break;
            }

            output.innerHTML = filteredMessages.map(msg => {
                const color = {
                    error: '#ff4444',
                    warn: '#ffaa00',
                    info: '#4488ff',
                    log: '#00ff00'
                }[msg.type] || '#00ff00';

                const prefix = msg.isExtension ? '[EXT] ' : '[APP] ';
                return `<span style="color: ${color}">[${msg.timestamp}] ${prefix}${msg.type.toUpperCase()}: ${msg.content}</span>`;
            }).join('\n');

            output.scrollTop = output.scrollHeight;
        }

        function showAll() {
            currentFilter = 'all';
            updateActiveButton('show-all');
            updateConsoleDisplay();
        }

        function showErrors() {
            currentFilter = 'errors';
            updateActiveButton('show-errors');
            updateConsoleDisplay();
        }

        function showWarnings() {
            currentFilter = 'warnings';
            updateActiveButton('show-warnings');
            updateConsoleDisplay();
        }

        function showAppLogs() {
            currentFilter = 'app';
            updateActiveButton('show-app');
            updateConsoleDisplay();
        }

        function hideExtensions() {
            currentFilter = 'hide-ext';
            updateActiveButton('hide-ext');
            updateConsoleDisplay();
        }

        function updateActiveButton(activeId) {
            document.querySelectorAll('.filter-buttons button').forEach(btn => {
                btn.classList.remove('active');
            });
            document.getElementById(activeId).classList.add('active');
        }

        function clearConsole() {
            consoleMessages = [];
            updateConsoleDisplay();
            console.log('🧹 Console cleared');
        }

        function filterErrors() {
            const realErrors = consoleMessages.filter(m => !m.isExtension && m.type === 'error');
            console.log('🔍 Real application errors found:', realErrors.length);
            if (realErrors.length === 0) {
                console.log('✅ No real application errors detected!');
            } else {
                realErrors.forEach(error => {
                    console.error('🚨 Real error:', error.content);
                });
            }
        }

        async function testWebContainer() {
            console.log('🧪 Testing WebContainer API...');
            try {
                if (!window.crossOriginIsolated) {
                    console.warn('⚠️ Cross-origin isolation not available');
                    return;
                }

                const { WebContainer } = await import('@webcontainer/api');
                console.log('✅ WebContainer API imported successfully');
                
                const webcontainer = await WebContainer.boot();
                console.log('✅ WebContainer booted successfully');
                
                await webcontainer.mount({
                    'test.txt': { file: { contents: 'Hello WebContainer!' } }
                });
                console.log('✅ File system test passed');
                
            } catch (error) {
                console.error('❌ WebContainer test failed:', error.message);
            }
        }

        function testCrossOrigin() {
            console.log('🧪 Testing cross-origin isolation...');
            console.log('Cross-origin isolated:', window.crossOriginIsolated);
            console.log('Secure context:', window.isSecureContext);
            console.log('Location:', window.location.href);
        }

        function testApplication() {
            console.log('🧪 Testing main application...');
            window.open('http://localhost:8082', '_blank');
            console.log('✅ Main application opened in new tab');
        }

        // Initialize
        setupConsoleCapture();
        console.log('🔧 Debug console initialized');
        console.log('📊 Monitoring console messages...');
    </script>
</body>
</html>
