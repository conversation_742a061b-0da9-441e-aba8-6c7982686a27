
import React, { useState, useEffect } from 'react';
import { ThemeProvider } from '../context/ThemeContext';
import Header from '../components/Header';
import { AIModel } from '../types';
import { MODEL_OPTIONS } from '../constants/models';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getApiKey, saveApiKey, getDefaultModel, saveDefaultModel, clearApiKey } from '../utils/storage';
import { ChevronLeft, CheckCircle2, Trash2, Eye, EyeOff } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';

const Settings = () => {
  const [selectedModel, setSelectedModel] = useState<AIModel>('openai');
  const [apiKey, setApiKey] = useState<string>('');
  const [showApiKey, setShowApiKey] = useState<boolean>(false);
  const [defaultModel, setDefaultModel] = useState<AIModel>('openai');
  const { toast } = useToast();

  // Load saved API key and default model when component mounts
  useEffect(() => {
    const savedKey = getApiKey(selectedModel);
    setApiKey(savedKey);
    
    const savedDefaultModel = getDefaultModel();
    if (savedDefaultModel) {
      setDefaultModel(savedDefaultModel);
    }
  }, [selectedModel]);

  const handleSaveApiKey = () => {
    if (apiKey.trim()) {
      saveApiKey(selectedModel, apiKey);
      toast({
        title: "API Key saved",
        description: `Your ${MODEL_OPTIONS.find(m => m.id === selectedModel)?.name} API key has been saved.`,
      });
    }
  };

  const handleClearApiKey = () => {
    clearApiKey(selectedModel);
    setApiKey('');
    toast({
      title: "API Key removed",
      description: `Your ${MODEL_OPTIONS.find(m => m.id === selectedModel)?.name} API key has been removed.`,
    });
  };

  const handleDefaultModelChange = (value: AIModel) => {
    setDefaultModel(value);
    saveDefaultModel(value);
    toast({
      title: "Default model updated",
      description: `${MODEL_OPTIONS.find(m => m.id === value)?.name} set as your default AI model.`,
    });
  };

  const toggleShowApiKey = () => {
    setShowApiKey(!showApiKey);
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen flex flex-col bg-background text-foreground">
        <Header />
        
        <main className="flex-1 container px-4 py-6 md:py-8">
          <div className="flex items-center gap-2 mb-6">
            <Link to="/" className="inline-flex items-center text-muted-foreground hover:text-foreground transition-colors group">
              <ChevronLeft className="w-4 h-4 mr-1 group-hover:translate-x-[-2px] transition-transform" />
              <span>Back to Home</span>
            </Link>
          </div>

          <div className="max-w-3xl mx-auto animate-fade-in">
            <h1 className="text-3xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-400">Settings</h1>
            
            <Tabs defaultValue="ai-models" className="w-full">
              <TabsList className="mb-6 bg-background/30 border border-border/40">
                <TabsTrigger value="ai-models" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                  AI Models
                </TabsTrigger>
                <TabsTrigger value="appearance" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                  Appearance
                </TabsTrigger>
                <TabsTrigger value="about" className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground">
                  About
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="ai-models" className="animate-fade-in">
                <div className="space-y-8">
                  <Card className="border border-border/60 bg-card/30 backdrop-blur-sm shadow-md transition-all hover:shadow-lg hover:border-border/80">
                    <CardHeader>
                      <CardTitle>Default AI Model</CardTitle>
                      <CardDescription>
                        Choose your preferred AI model to generate code by default
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <RadioGroup 
                        value={defaultModel} 
                        onValueChange={(value) => handleDefaultModelChange(value as AIModel)}
                        className="grid gap-4"
                      >
                        {MODEL_OPTIONS.map((model) => {
                          const hasApiKey = !!getApiKey(model.id as AIModel);
                          return (
                            <div key={model.id} className={cn(
                              "flex items-center space-x-2 rounded-md border p-4 transition-all",
                              defaultModel === model.id ? "border-primary bg-primary/5" : "border-border/40",
                              !hasApiKey && "opacity-60"
                            )}>
                              <RadioGroupItem value={model.id} id={`default-${model.id}`} disabled={!hasApiKey} />
                              <Label 
                                htmlFor={`default-${model.id}`} 
                                className="flex flex-1 items-center justify-between cursor-pointer"
                              >
                                <div>
                                  <p className="font-medium">{model.name}</p>
                                  <p className="text-sm text-muted-foreground">{model.description}</p>
                                </div>
                                {hasApiKey ? (
                                  <Badge variant="outline" className="bg-green-500/10 text-green-500 border-green-500/20">
                                    <CheckCircle2 className="h-3 w-3 mr-1" />
                                    Configured
                                  </Badge>
                                ) : (
                                  <Badge variant="outline" className="bg-amber-500/10 text-amber-500 border-amber-500/20">
                                    API Key Required
                                  </Badge>
                                )}
                              </Label>
                            </div>
                          );
                        })}
                      </RadioGroup>
                    </CardContent>
                  </Card>
                
                  <Card className="border border-border/60 bg-card/30 backdrop-blur-sm shadow-md transition-all hover:shadow-lg hover:border-border/80">
                    <CardHeader>
                      <CardTitle>API Keys Configuration</CardTitle>
                      <CardDescription>
                        Manage your API keys for different AI models. Keys are stored locally in your browser.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="space-y-2">
                        <Label htmlFor="model-select">Select AI Model</Label>
                        <Select 
                          value={selectedModel} 
                          onValueChange={(value) => setSelectedModel(value as AIModel)}
                        >
                          <SelectTrigger id="model-select" className="bg-background/50 border-border/60">
                            <SelectValue placeholder="Select a model" />
                          </SelectTrigger>
                          <SelectContent>
                            {MODEL_OPTIONS.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                <span className="flex items-center">
                                  {model.name}
                                  {getApiKey(model.id as AIModel) && (
                                    <CheckCircle2 className="h-3 w-3 ml-2 text-green-500" />
                                  )}
                                </span>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-muted-foreground mt-1">
                          {MODEL_OPTIONS.find(m => m.id === selectedModel)?.description}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="api-key">API Key</Label>
                        <div className="flex gap-2">
                          <div className="flex-1 relative">
                            <Input
                              id="api-key"
                              type={showApiKey ? "text" : "password"}
                              placeholder={MODEL_OPTIONS.find(m => m.id === selectedModel)?.apiKeyPlaceholder}
                              value={apiKey}
                              onChange={(e) => setApiKey(e.target.value)}
                              className="flex-1 bg-background/50 border-border/60 pr-10"
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full"
                              onClick={toggleShowApiKey}
                            >
                              {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              <span className="sr-only">{showApiKey ? "Hide API Key" : "Show API Key"}</span>
                            </Button>
                          </div>
                          <Button 
                            onClick={handleSaveApiKey} 
                            disabled={!apiKey.trim()}
                            className="bg-primary/90 hover:bg-primary transition-all"
                          >
                            Save Key
                          </Button>
                          {apiKey && (
                            <Button
                              variant="outline"
                              onClick={handleClearApiKey}
                              title="Remove API key"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Remove API key</span>
                            </Button>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          Your API key is stored locally and never sent to our servers
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="appearance" className="animate-fade-in">
                <Card className="border border-border/60 bg-card/30 backdrop-blur-sm shadow-md">
                  <CardHeader>
                    <CardTitle>Appearance Settings</CardTitle>
                    <CardDescription>
                      Customize the look and feel of the application
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      More appearance settings coming soon. Currently, you can toggle dark mode from the header.
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="about" className="animate-fade-in">
                <Card className="border border-border/60 bg-card/30 backdrop-blur-sm shadow-md">
                  <CardHeader>
                    <CardTitle>About WebGen</CardTitle>
                    <CardDescription>
                      AI-powered website generator
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p>
                      WebGen is an AI-powered website generator that helps you create complete websites
                      with just a text prompt. It leverages advanced AI models to generate 
                      responsive and functional code.
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Version 1.0.0
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </main>
      </div>
    </ThemeProvider>
  );
};

export default Settings;
