
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ThemeProvider } from '../context/ThemeContext';
import Header from '../components/Header';
import PromptForm from '../components/PromptForm';
import CodeEditor from '../components/CodeEditor';
import LivePreview from '../components/LivePreview';
import { AIModel, GeneratedFile, GenerationResult } from '../types';
import { generateCode } from '../services/aiService';
import { savePromptHistory } from '../utils/storage';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent } from '@/components/ui/card';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';

const Index = () => {
  const navigate = useNavigate();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleGenerate = async (prompt: string, model: AIModel, apiKey: string) => {
    setIsGenerating(true);
    setError(null);

    try {
      const files = await generateCode({ prompt, model, apiKey });
      setGeneratedFiles(files);
      
      // Save to history
      const result: GenerationResult = {
        files,
        timestamp: Date.now(),
      };
      savePromptHistory(prompt, model, result);
      
      toast({
        title: "Generation complete!",
        description: "Redirecting to editor...",
      });

      // Redirect to project editor with generated files
      setTimeout(() => {
        navigate('/editor', {
          state: {
            files,
            history: [
              { role: 'user', content: prompt },
              { role: 'assistant', content: 'I\'ve generated your website successfully! You can now use the regenerate section feature to modify specific parts.' }
            ],
            model,
            prompt
          }
        });
      }, 1000);
      
    } catch (err) {
      console.error('Generation error:', err);
      setError(err instanceof Error ? err.message : String(err));
      
      toast({
        variant: "destructive",
        title: "Generation failed",
        description: err instanceof Error ? err.message : String(err),
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <ThemeProvider>
      <div className="min-h-screen flex flex-col bg-background text-foreground overflow-hidden">
        <Header />
        
        <main className="flex-1 flex flex-col h-[calc(100vh-64px)] overflow-hidden">
          <div className="container px-4 py-4 md:px-6 md:py-6 flex flex-col h-full">
            <div className="mb-4 md:mb-6">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-purple-400 tracking-tight">
                Generate Complete Websites with AI
              </h1>
              <p className="text-base md:text-lg text-muted-foreground mt-2">
                Describe your website and our AI will generate all the necessary code for you.
              </p>
            </div>
            
            <PromptForm isGenerating={isGenerating} onGenerate={handleGenerate} />
            
            {error && (
              <Alert variant="destructive" className="mb-4 md:mb-6 border border-destructive/20 bg-destructive/5 shadow-sm">
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
            
            {generatedFiles.length > 0 ? (
              <Card className="flex-1 min-h-0 overflow-hidden border-border/40 bg-card/30 backdrop-blur-sm shadow-md hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-0 h-full">
                  <ResizablePanelGroup
                    direction="horizontal"
                    className="h-full animate-fade-in rounded-lg overflow-hidden"
                  >
                    <ResizablePanel defaultSize={40} minSize={20} maxSize={80}>
                      <CodeEditor files={generatedFiles} />
                    </ResizablePanel>
                    <ResizableHandle withHandle />
                    <ResizablePanel defaultSize={60} minSize={20} maxSize={80}>
                      <LivePreview files={generatedFiles} />
                    </ResizablePanel>
                  </ResizablePanelGroup>
                </CardContent>
              </Card>
            ) : (
              <Card className="flex-1 min-h-0 overflow-hidden border-border/40 bg-card/30 backdrop-blur-sm shadow-md">
                <CardContent className="p-8 h-full flex items-center justify-center">
                  <div className="text-center max-w-md">
                    <div className="mb-6">
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
                        <svg className="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                        </svg>
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold mb-3 text-foreground">Ready to Generate Your Website</h3>
                    <p className="text-muted-foreground mb-6 leading-relaxed">
                      Describe your ideal website above, and our AI will generate complete, production-ready code with live preview.
                    </p>
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <span>HTML, CSS & JavaScript</span>
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                        <span>Responsive Design</span>
                      </div>
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                        <span>Modern Components</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </main>
      </div>
    </ThemeProvider>
  );
};

export default Index;
