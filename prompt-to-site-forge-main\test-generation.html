<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Website Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <h1>Website Generation Test</h1>
    
    <div class="test-section">
        <h2>Test AI Service</h2>
        <p>Test the improved AI service with different prompts:</p>
        
        <button onclick="testPrompt('Create a modern portfolio website for John Doe')">Test Portfolio</button>
        <button onclick="testPrompt('Build a restaurant website for Bella Vista with dark theme')">Test Restaurant</button>
        <button onclick="testPrompt('Make a creative agency website with colorful design')">Test Agency</button>
        
        <div id="test-results"></div>
    </div>

    <script type="module">
        // Import the AI service (this would normally be done through the React app)
        window.testPrompt = async function(prompt) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<p>Testing prompt: "' + prompt + '"</p><p>Loading...</p>';
            
            try {
                // Simulate the AI service call
                const analysis = analyzePrompt(prompt);
                
                resultsDiv.innerHTML = `
                    <div class="result">
                        <h3>Analysis Results:</h3>
                        <pre>${JSON.stringify(analysis, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultsDiv.innerHTML = `
                    <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24;">
                        <h3>Error:</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        };
        
        // Simple version of the analyzePrompt function for testing
        function analyzePrompt(prompt) {
            const lowerPrompt = prompt.toLowerCase();
            
            // Extract business name
            let businessName = 'Your Business';
            const namePatterns = [
                /(?:for|called|named|about)\s+([a-zA-Z\s]+?)(?:\s|$|,|\.|!|\?)/,
                /([a-zA-Z\s]+?)\s+(?:website|site|page|business|company)/,
                /"([^"]+)"/,
                /'([^']+)'/
            ];
            
            for (const pattern of namePatterns) {
                const match = prompt.match(pattern);
                if (match && match[1] && match[1].trim().length > 1) {
                    businessName = match[1].trim();
                    businessName = businessName.replace(/\b\w/g, l => l.toUpperCase());
                    break;
                }
            }
            
            // Determine business type
            let businessType = 'business';
            const typeKeywords = {
                'restaurant': ['restaurant', 'cafe', 'diner', 'bistro', 'eatery', 'food', 'dining'],
                'portfolio': ['portfolio', 'showcase', 'artist', 'designer', 'photographer', 'creative'],
                'agency': ['agency', 'studio', 'firm', 'consultancy', 'marketing'],
                'ecommerce': ['shop', 'store', 'ecommerce', 'retail', 'marketplace', 'selling'],
                'blog': ['blog', 'news', 'magazine', 'journal', 'articles'],
                'personal': ['personal', 'freelance', 'individual', 'me', 'myself']
            };
            
            for (const [type, keywords] of Object.entries(typeKeywords)) {
                if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
                    businessType = type;
                    break;
                }
            }
            
            // Determine style
            let style = 'modern';
            if (lowerPrompt.includes('dark') || lowerPrompt.includes('black')) style = 'dark';
            else if (lowerPrompt.includes('colorful') || lowerPrompt.includes('vibrant')) style = 'colorful';
            else if (lowerPrompt.includes('creative') || lowerPrompt.includes('artistic')) style = 'creative';
            else if (lowerPrompt.includes('modern') || lowerPrompt.includes('minimalist')) style = 'modern';
            
            return {
                businessName,
                businessType,
                style,
                originalPrompt: prompt,
                features: ['About Section', 'Services', 'Contact Form'],
                detectedKeywords: {
                    isDark: lowerPrompt.includes('dark'),
                    isColorful: lowerPrompt.includes('colorful'),
                    isCreative: lowerPrompt.includes('creative'),
                    isModern: lowerPrompt.includes('modern')
                }
            };
        }
    </script>
</body>
</html>
