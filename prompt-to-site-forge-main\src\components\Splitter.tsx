
import React, { useState, useEffect, useRef } from 'react';

interface SplitterProps {
  left: React.ReactNode;
  right: React.ReactNode;
  initialLeftWidth?: number;
  className?: string;
  showDragHandle?: boolean;
}

const Splitter: React.FC<SplitterProps> = ({ 
  left, 
  right, 
  initialLeftWidth = 50,
  className = '',
  showDragHandle = true
}) => {
  const [leftWidth, setLeftWidth] = useState(initialLeftWidth);
  const [dragging, setDragging] = useState(false);
  const splitterRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!dragging || !containerRef.current) return;
      
      const containerRect = containerRef.current.getBoundingClientRect();
      const newLeftWidth = ((e.clientX - containerRect.left) / containerRect.width) * 100;
      
      // Constrain between 20% and 80%
      setLeftWidth(Math.min(Math.max(newLeftWidth, 20), 80));
    };

    const handleMouseUp = () => {
      setDragging(false);
      document.body.classList.remove('select-none');
    };

    if (dragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.classList.add('select-none');
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.classList.remove('select-none');
    };
  }, [dragging]);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setDragging(true);
  };

  return (
    <div 
      ref={containerRef} 
      className={`splitter-layout h-full flex ${className}`}
    >
      <div 
        className="layout-pane h-full transition-all duration-300 ease-in-out" 
        style={{ width: `${leftWidth}%` }}
      >
        {left}
      </div>
      {showDragHandle && (
        <div 
          ref={splitterRef}
          className={`layout-splitter bg-border/80 hover:bg-primary/70 transition-colors relative z-10 ${dragging ? 'bg-primary/70' : ''}`}
          onMouseDown={handleMouseDown}
          style={{ 
            cursor: 'col-resize',
            width: '4px',
          }}
        >
          <div className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-5 h-12 flex flex-col items-center justify-center">
            <div className="w-1 h-8 flex flex-col justify-center items-center gap-1.5">
              <div className="w-1 h-1 rounded-full bg-primary/70 dark:bg-primary/90"></div>
              <div className="w-1 h-1 rounded-full bg-primary/70 dark:bg-primary/90"></div>
              <div className="w-1 h-1 rounded-full bg-primary/70 dark:bg-primary/90"></div>
            </div>
          </div>
        </div>
      )}
      <div 
        className="layout-pane h-full transition-all duration-300 ease-in-out" 
        style={{ width: `${100 - leftWidth}%` }}
      >
        {right}
      </div>
    </div>
  );
};

export default Splitter;
