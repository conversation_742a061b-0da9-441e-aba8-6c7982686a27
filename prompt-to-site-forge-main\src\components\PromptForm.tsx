
import React, { useState, useEffect } from 'react';
import { AIModel } from '../types';
import { EXAMPLE_PROMPTS } from '../constants/models';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Settings, CheckCircle2, ChevronDown } from 'lucide-react';
import { Link } from 'react-router-dom';
import { getApiKey, getDefaultModel, saveDefaultModel } from '../utils/storage';
import { MODEL_OPTIONS } from '../constants/models';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { 
  Popover,
  PopoverContent,
  PopoverTrigger 
} from '@/components/ui/popover';

interface PromptFormProps {
  isGenerating: boolean;
  onGenerate: (prompt: string, model: AIModel, apiKey: string) => void;
}

const PromptForm: React.FC<PromptFormProps> = ({ isGenerating, onGenerate }) => {
  const [prompt, setPrompt] = useState<string>('');
  const [selectedModel, setSelectedModel] = useState<AIModel>('openai');
  const [apiKey, setApiKey] = useState<string>('');
  const [isExamplesOpen, setIsExamplesOpen] = useState(true);

  // Load saved API key and default model when component mounts
  useEffect(() => {
    const savedKey = getApiKey(selectedModel);
    setApiKey(savedKey);
    
    const defaultModel = getDefaultModel();
    if (defaultModel) {
      setSelectedModel(defaultModel);
    }
  }, []);

  // Update API key when selected model changes
  useEffect(() => {
    const savedKey = getApiKey(selectedModel);
    setApiKey(savedKey);
  }, [selectedModel]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!prompt.trim()) {
      return;
    }
    
    onGenerate(prompt, selectedModel, apiKey);
  };

  const handleExamplePrompt = (example: string) => {
    setPrompt(example);
  };

  const handleModelChange = (value: string) => {
    const model = value as AIModel;
    setSelectedModel(model);
    saveDefaultModel(model);
  };

  const getSelectedModelName = () => {
    const model = MODEL_OPTIONS.find(m => m.id === selectedModel);
    return model ? model.name : 'Select Model';
  };

  const toggleExamples = () => {
    setIsExamplesOpen(!isExamplesOpen);
  };

  return (
    <Card className="mb-6 overflow-visible border border-border/40 bg-card/30 backdrop-blur-sm shadow-lg">
      <CardContent className="pt-6 pb-6">
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="flex items-center justify-between mb-2">
            <Label htmlFor="prompt" className="text-lg font-medium flex items-center">
              <span className="bg-primary/10 text-primary rounded-md w-6 h-6 inline-flex items-center justify-center mr-2 text-sm font-bold">
                1
              </span>
              Describe your website
            </Label>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Link to="/settings" className="inline-flex items-center text-sm text-primary hover:underline font-medium">
                    <Settings className="h-4 w-4 mr-1.5" />
                    Configure API Keys
                  </Link>
                </TooltipTrigger>
                <TooltipContent>
                  Configure AI models and API keys
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
          
          <div className="space-y-2">
            <Textarea
              id="prompt"
              placeholder="Describe the website you want to generate..."
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              className="min-h-[120px] text-base bg-background/50 border-border/60 focus:border-primary resize-none transition-all p-4"
              required
            />
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between cursor-pointer select-none" onClick={toggleExamples}>
              <div className="flex items-center">
                <span className="bg-primary/10 text-primary rounded-md w-6 h-6 inline-flex items-center justify-center mr-2 text-sm font-bold">
                  2
                </span>
                <span className="text-sm font-medium">Example prompts</span>
              </div>
              <ChevronDown className={`h-4 w-4 text-muted-foreground transition-transform ${isExamplesOpen ? 'rotate-180' : ''}`} />
            </div>
            
            {isExamplesOpen && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 pt-1 animate-accordion-down pl-8">
                {EXAMPLE_PROMPTS.map((example, index) => (
                  <Button
                    key={index}
                    variant="outline"
                    type="button"
                    onClick={() => handleExamplePrompt(example)}
                    className="text-xs justify-start h-auto py-2 px-3 overflow-hidden text-ellipsis whitespace-nowrap bg-background/30 border-border/40 hover:bg-primary/5 hover:border-primary/30 transition-all"
                    title={example}
                  >
                    {example.length > 40 ? example.substring(0, 37) + '...' : example}
                  </Button>
                ))}
              </div>
            )}
          </div>
          
          <div className="flex flex-col sm:flex-row items-end justify-between gap-3 pt-3">
            <div className="w-full sm:w-auto flex items-center">
              <span className="bg-primary/10 text-primary rounded-md w-6 h-6 inline-flex items-center justify-center mr-2 text-sm font-bold">
                3
              </span>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link to="/settings" className="text-xs text-muted-foreground hover:text-primary hover:underline">
                      Need to configure an API key?
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>
                    Set up your API keys in settings
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
            
            <div className="flex w-full sm:w-auto items-center gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 bg-background/50 border-border/60"
                  >
                    <CheckCircle2 
                      className={cn("h-3 w-3 mr-1", apiKey ? "text-green-500" : "text-red-500")} 
                    />
                    <span className="text-xs">{getSelectedModelName()}</span>
                    <ChevronDown className="h-3 w-3 ml-1 opacity-70" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2">
                  <div className="space-y-1">
                    {MODEL_OPTIONS.map((model) => (
                      <Button
                        key={model.id}
                        variant={selectedModel === model.id ? "secondary" : "ghost"}
                        size="sm"
                        className="w-full justify-start text-xs"
                        onClick={() => handleModelChange(model.id)}
                      >
                        <CheckCircle2 
                          className={cn(
                            "h-3 w-3 mr-2", 
                            getApiKey(model.id as AIModel) ? "text-green-500" : "text-red-500"
                          )} 
                        />
                        {model.name}
                      </Button>
                    ))}
                    <div className="pt-1 border-t border-border mt-1">
                      <Link to="/settings">
                        <Button variant="ghost" size="sm" className="w-full justify-start text-xs">
                          <Settings className="h-3 w-3 mr-2" />
                          Configure API Keys
                        </Button>
                      </Link>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
              
              <Button 
                type="submit" 
                disabled={isGenerating || !prompt.trim() || !apiKey.trim()}
                className="flex-1 sm:flex-none bg-primary/90 hover:bg-primary transition-all shadow-md"
              >
                {isGenerating ? 'Generating...' : 'Generate Website'}
              </Button>
            </div>
          </div>
          
          {!apiKey && (
            <div className="text-xs text-amber-500 text-center mt-2">
              API key missing. Please set it in <Link to="/settings" className="underline">settings</Link>.
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};

export default PromptForm;
