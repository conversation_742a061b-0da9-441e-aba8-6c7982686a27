import { GeneratedFile, AIModel } from "../types";
import { callAI } from "./apiClients";
import { getSystemPrompt, GENERATION_PROMPT } from "../constants/prompts";

interface GenerateOptions {
  prompt: string;
  model: AIModel;
  apiKey: string;
}

interface RegenerateSectionOptions {
  originalPrompt: string;
  sectionPrompt: string;
  sectionId: string;
  currentFiles: GeneratedFile[];
  model: string;
}

interface ChatModificationOptions {
  prompt: string;
  currentFiles: GeneratedFile[];
  chatHistory: Array<{role: 'user' | 'assistant', content: string}>;
  model: string;
}

// Helper to generate unique IDs
const generateUniqueId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Parse AI response to extract code files
const parseAIResponse = (content: string): GeneratedFile[] => {
  const files: GeneratedFile[] = [];

  // Look for code blocks with file names
  const codeBlockRegex = /```(?:html|css|javascript|js)\s*(?:\/\/\s*(.+?\.(?:html|css|js))\s*)?\n([\s\S]*?)```/gi;
  const fileNameRegex = /(?:index\.html|styles\.css|script\.js|main\.js)/i;

  let match;
  const foundFiles = new Set<string>();

  while ((match = codeBlockRegex.exec(content)) !== null) {
    const [, fileName, code] = match;
    let detectedFileName = fileName;

    // If no filename in comment, try to detect from content
    if (!detectedFileName) {
      if (code.includes('<!DOCTYPE html') || code.includes('<html')) {
        detectedFileName = 'index.html';
      } else if (code.includes('body {') || code.includes('.container') || code.includes('@media')) {
        detectedFileName = 'styles.css';
      } else if (code.includes('function') || code.includes('document.') || code.includes('addEventListener')) {
        detectedFileName = 'script.js';
      }
    }

    if (detectedFileName && fileNameRegex.test(detectedFileName) && !foundFiles.has(detectedFileName)) {
      foundFiles.add(detectedFileName);

      const language = detectedFileName.endsWith('.html') ? 'html' :
                      detectedFileName.endsWith('.css') ? 'css' : 'javascript';

      files.push({
        id: generateUniqueId(),
        name: detectedFileName,
        content: code.trim(),
        language
      });
    }
  }

  // If no files found, try alternative parsing
  if (files.length === 0) {
    // Look for HTML content
    const htmlMatch = content.match(/<!DOCTYPE html[\s\S]*?<\/html>/i);
    if (htmlMatch) {
      files.push({
        id: generateUniqueId(),
        name: 'index.html',
        content: htmlMatch[0],
        language: 'html'
      });
    }

    // Look for CSS content
    const cssMatch = content.match(/\/\*[\s\S]*?\*\/|[^{}]*\{[^{}]*\}/g);
    if (cssMatch && cssMatch.length > 0) {
      files.push({
        id: generateUniqueId(),
        name: 'styles.css',
        content: cssMatch.join('\n'),
        language: 'css'
      });
    }
  }

  return files;
};

// Enhanced prompt analysis with better keyword detection
const analyzePrompt = (prompt: string) => {
  const lowerPrompt = prompt.toLowerCase();
  
  // Extract business name with better patterns
  let businessName = 'My Website';
  const namePatterns = [
    /(?:for|called|named|about|website for|site for)\s+([A-Za-z0-9\s&'-]+?)(?:\s|$|\.|\,|:|;)/i,
    /([A-Za-z0-9\s&'-]+)\s+(?:website|site|page|business|company|store|shop|restaurant|cafe)/i,
    /create\s+(?:a\s+)?(?:website\s+for\s+)?([A-Za-z0-9\s&'-]+)/i,
    /build\s+(?:a\s+)?(?:website\s+for\s+)?([A-Za-z0-9\s&'-]+)/i
  ];
  
  for (const pattern of namePatterns) {
    const match = prompt.match(pattern);
    if (match && match[1] && match[1].trim().length > 1) {
      businessName = match[1].trim().split(' ').slice(0, 4).join(' ');
      // Clean up common words
      businessName = businessName.replace(/\b(a|an|the|my|our|website|site|page)\b/gi, '').trim();
      if (businessName.length > 2) break;
    }
  }

  // Enhanced business type detection
  let businessType = 'business';
  const typeMap = {
    'restaurant': ['restaurant', 'cafe', 'diner', 'bistro', 'eatery', 'food', 'kitchen', 'bar', 'pub'],
    'portfolio': ['portfolio', 'personal', 'resume', 'cv', 'showcase', 'designer', 'developer', 'artist'],
    'ecommerce': ['store', 'shop', 'ecommerce', 'sell', 'products', 'marketplace', 'online store', 'retail'],
    'blog': ['blog', 'news', 'articles', 'writing', 'journal', 'magazine', 'content'],
    'agency': ['agency', 'consulting', 'services', 'firm', 'studio', 'company', 'consultancy'],
    'landing': ['landing', 'product', 'launch', 'startup', 'saas', 'app', 'software'],
    'healthcare': ['medical', 'doctor', 'clinic', 'health', 'hospital', 'dentist', 'pharmacy'],
    'education': ['school', 'education', 'learning', 'course', 'university', 'academy', 'training'],
    'real estate': ['real estate', 'property', 'homes', 'realty', 'realtor', 'housing'],
    'fitness': ['gym', 'fitness', 'workout', 'exercise', 'training', 'yoga', 'sports'],
    'travel': ['travel', 'tourism', 'vacation', 'trip', 'hotel', 'booking', 'destinations'],
    'nonprofit': ['nonprofit', 'charity', 'foundation', 'organization', 'volunteer', 'donation'],
    'law': ['law', 'lawyer', 'attorney', 'legal', 'firm', 'justice'],
    'finance': ['finance', 'bank', 'investment', 'financial', 'accounting', 'tax']
  };

  for (const [type, keywords] of Object.entries(typeMap)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      businessType = type;
      break;
    }
  }

  // Extract specific features and sections
  const features = [];
  const featureKeywords = {
    'Contact Form': ['contact', 'get in touch', 'reach us', 'email us', 'contact form'],
    'About Section': ['about', 'story', 'history', 'team', 'who we are'],
    'Services': ['services', 'what we do', 'offerings', 'solutions'],
    'Gallery': ['gallery', 'photos', 'images', 'portfolio', 'showcase'],
    'Testimonials': ['testimonials', 'reviews', 'feedback', 'clients say'],
    'Pricing': ['pricing', 'packages', 'plans', 'cost', 'rates'],
    'Blog': ['blog', 'news', 'articles', 'posts'],
    'FAQ': ['faq', 'questions', 'help', 'support'],
    'Newsletter': ['newsletter', 'subscribe', 'signup', 'email list'],
    'Social Media': ['social', 'facebook', 'twitter', 'instagram', 'linkedin'],
    'Location/Map': ['location', 'address', 'map', 'directions', 'where'],
    'Booking/Reservation': ['booking', 'reservation', 'appointment', 'schedule'],
    'Shopping Cart': ['cart', 'checkout', 'purchase', 'buy now', 'add to cart'],
    'Search': ['search', 'find', 'filter', 'browse'],
    'User Authentication': ['login', 'register', 'account', 'profile', 'sign up']
  };

  for (const [feature, keywords] of Object.entries(featureKeywords)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      features.push(feature);
    }
  }

  // Color scheme extraction with better detection
  const colors = [];
  const colorRegex = /(blue|red|green|purple|yellow|orange|pink|black|white|gray|grey|navy|teal|cyan|indigo|violet|crimson|emerald|amber|lime|rose|sky|slate|dark|light|bright|vibrant)/gi;
  const colorMatches = prompt.match(colorRegex);
  if (colorMatches) {
    colors.push(...[...new Set(colorMatches.slice(0, 3))]);
  }

  // Style detection
  let style = 'modern';
  const styleKeywords = {
    'modern': ['modern', 'sleek', 'contemporary', 'minimalist', 'clean'],
    'classic': ['classic', 'traditional', 'elegant', 'sophisticated', 'timeless'],
    'creative': ['creative', 'artistic', 'unique', 'innovative', 'bold'],
    'professional': ['professional', 'corporate', 'business', 'formal'],
    'fun': ['fun', 'playful', 'colorful', 'vibrant', 'energetic'],
    'luxury': ['luxury', 'premium', 'high-end', 'exclusive', 'upscale']
  };

  for (const [styleType, keywords] of Object.entries(styleKeywords)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      style = styleType;
      break;
    }
  }

  // Extract target audience
  let audience = 'general';
  const audienceKeywords = {
    'young adults': ['young', 'millennials', 'students', 'teens'],
    'professionals': ['professionals', 'executives', 'business', 'corporate'],
    'families': ['families', 'parents', 'children', 'kids'],
    'seniors': ['seniors', 'elderly', 'retirement', 'mature'],
    'tech-savvy': ['tech', 'developers', 'programmers', 'digital']
  };

  for (const [audienceType, keywords] of Object.entries(audienceKeywords)) {
    if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
      audience = audienceType;
      break;
    }
  }

  return {
    businessName,
    businessType,
    features: features.length > 0 ? features : getDefaultFeatures(businessType),
    colors: colors.length > 0 ? colors : getDefaultColors(businessType),
    style,
    audience,
    description: prompt.length > 100 ? prompt.substring(0, 200) + '...' : prompt,
    originalPrompt: prompt
  };
};

// Get default features based on business type
const getDefaultFeatures = (businessType: string): string[] => {
  const defaultFeatures = {
    restaurant: ['About Section', 'Services', 'Contact Form', 'Location/Map', 'Booking/Reservation'],
    portfolio: ['About Section', 'Gallery', 'Services', 'Contact Form', 'Testimonials'],
    ecommerce: ['Services', 'Gallery', 'Shopping Cart', 'Search', 'User Authentication'],
    blog: ['About Section', 'Blog', 'Newsletter', 'Social Media', 'Search'],
    agency: ['About Section', 'Services', 'Gallery', 'Testimonials', 'Contact Form'],
    landing: ['About Section', 'Services', 'Pricing', 'Newsletter', 'Contact Form'],
    healthcare: ['About Section', 'Services', 'Contact Form', 'Location/Map', 'Booking/Reservation'],
    education: ['About Section', 'Services', 'Blog', 'Contact Form', 'FAQ'],
    'real estate': ['About Section', 'Services', 'Gallery', 'Contact Form', 'Search'],
    fitness: ['About Section', 'Services', 'Pricing', 'Contact Form', 'Booking/Reservation'],
    travel: ['About Section', 'Services', 'Gallery', 'Contact Form', 'Booking/Reservation'],
    nonprofit: ['About Section', 'Services', 'Blog', 'Contact Form', 'Newsletter'],
    law: ['About Section', 'Services', 'Contact Form', 'FAQ', 'Testimonials'],
    finance: ['About Section', 'Services', 'Contact Form', 'FAQ', 'Testimonials']
  };
  
  return defaultFeatures[businessType] || ['About Section', 'Services', 'Contact Form'];
};

// Get default colors based on business type
const getDefaultColors = (businessType: string): string[] => {
  const defaultColors = {
    restaurant: ['orange', 'brown'],
    portfolio: ['blue', 'gray'],
    ecommerce: ['green', 'white'],
    blog: ['purple', 'white'],
    agency: ['blue', 'dark'],
    landing: ['blue', 'white'],
    healthcare: ['blue', 'white'],
    education: ['blue', 'green'],
    'real estate': ['blue', 'gray'],
    fitness: ['red', 'black'],
    travel: ['blue', 'green'],
    nonprofit: ['green', 'blue'],
    law: ['navy', 'gold'],
    finance: ['navy', 'green']
  };
  
  return defaultColors[businessType] || ['blue', 'white'];
};

// Generate dynamic HTML content
const generateDynamicHTML = (analysis: any): string => {
  const { businessName, businessType, features, description } = analysis;

  // Generate navigation based on features
  const navItems = features.map(feature => {
    const id = feature.toLowerCase().replace(/[^a-z0-9]/g, '-');
    return `<a href="#${id}" class="nav-link">${feature.replace(' Section', '').replace(' Form', '')}</a>`;
  }).join('\n                ');

  // Generate sections based on features
  const sections = features.map(feature => {
    const id = feature.toLowerCase().replace(/[^a-z0-9]/g, '-');
    return generateFeatureSection(feature, id, analysis);
  }).join('\n\n        ');

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${businessName}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header data-section-id="header" data-section-name="Header">
        <div class="container">
            <div class="logo">
                <h1>${businessName}</h1>
            </div>
            <nav class="nav">
                ${navItems}
            </nav>
            <button class="menu-toggle" aria-label="Toggle menu">☰</button>
        </div>
    </header>

    <main>
        <section class="hero" data-section-id="hero" data-section-name="Hero Section">
            <div class="container">
                <div class="hero-content">
                    <h1>Welcome to ${businessName}</h1>
                    <p>${description}</p>
                    <div class="hero-buttons">
                        <button class="btn btn-primary">Get Started</button>
                        <button class="btn btn-secondary">Learn More</button>
                    </div>
                </div>
            </div>
        </section>

        ${sections}
    </main>

    <footer data-section-id="footer" data-section-name="Footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>${businessName}</h3>
                    <p>Your trusted ${businessType} solution.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        ${features.slice(0, 4).map(feature => 
                          `<li><a href="#${feature.toLowerCase().replace(/[^a-z0-9]/g, '-')}">${feature.replace(' Section', '').replace(' Form', '')}</a></li>`
                        ).join('')}
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p>📧 info@${businessName.toLowerCase().replace(/\s+/g, '')}.com</p>
                    <p>📞 (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 ${businessName}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>`;
};

// Generate specific feature sections
const generateFeatureSection = (feature: string, id: string, analysis: any): string => {
  const { businessName, businessType } = analysis;

  switch (feature) {
    case 'About Section':
      return `<section class="about" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>About ${businessName}</h2>
                <div class="about-content">
                    <div class="about-text">
                        <p>At ${businessName}, we are passionate about delivering exceptional ${businessType} services that exceed your expectations. Our team is dedicated to providing you with the best possible experience.</p>
                        <p>With years of experience in the industry, we understand what it takes to create lasting relationships with our clients. We believe in quality, innovation, and customer satisfaction.</p>
                    </div>
                    <div class="about-stats">
                        <div class="stat">
                            <h3>500+</h3>
                            <p>Happy Clients</p>
                        </div>
                        <div class="stat">
                            <h3>5+</h3>
                            <p>Years Experience</p>
                        </div>
                        <div class="stat">
                            <h3>100%</h3>
                            <p>Satisfaction</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>`;

    case 'Services':
      const services = getBusinessServices(businessType);
      return `<section class="services" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>Our Services</h2>
                <div class="services-grid">
                    ${services.map(service => `
                    <div class="service-card">
                        <div class="service-icon">${service.icon}</div>
                        <h3>${service.title}</h3>
                        <p>${service.description}</p>
                        <button class="btn btn-outline">Learn More</button>
                    </div>`).join('')}
                </div>
            </div>
        </section>`;

    case 'Contact Form':
      return `<section class="contact" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>Contact ${businessName}</h2>
                <div class="contact-content">
                    <div class="contact-info">
                        <h3>Get in Touch</h3>
                        <p>Ready to work with us? Contact us today and let's discuss how we can help you achieve your goals.</p>
                        <div class="contact-details">
                            <div class="contact-item">
                                <span class="icon">📧</span>
                                <span>info@${businessName.toLowerCase().replace(/\s+/g, '')}.com</span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">📞</span>
                                <span>(*************</span>
                            </div>
                            <div class="contact-item">
                                <span class="icon">📍</span>
                                <span>123 Main Street, City, State 12345</span>
                            </div>
                        </div>
                    </div>
                    <form class="contact-form">
                        <div class="form-row">
                            <input type="text" placeholder="Your Name" required>
                            <input type="email" placeholder="Your Email" required>
                        </div>
                        <input type="text" placeholder="Subject" required>
                        <textarea placeholder="Your Message" rows="5" required></textarea>
                        <button type="submit" class="btn btn-primary">Send Message</button>
                    </form>
                </div>
            </div>
        </section>`;

    case 'Gallery':
      return `<section class="gallery" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>Gallery</h2>
                <div class="gallery-grid">
                    ${Array.from({length: 6}, (_, i) => `
                    <div class="gallery-item">
                        <div class="gallery-placeholder">
                            <span>Image ${i + 1}</span>
                        </div>
                    </div>`).join('')}
                </div>
            </div>
        </section>`;

    case 'Testimonials':
      const testimonials = [
        { name: "Sarah Johnson", text: "Excellent service and professional team. Highly recommended!", rating: 5 },
        { name: "Mike Chen", text: "Outstanding results and great communication throughout the process.", rating: 5 },
        { name: "Emily Davis", text: "Professional, reliable, and delivered exactly what we needed.", rating: 5 }
      ];
      return `<section class="testimonials" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>What Our Clients Say</h2>
                <div class="testimonials-grid">
                    ${testimonials.map(testimonial => `
                    <div class="testimonial-card">
                        <div class="testimonial-rating">
                            ${'★'.repeat(testimonial.rating)}
                        </div>
                        <p>"${testimonial.text}"</p>
                        <div class="testimonial-author">
                            <strong>${testimonial.name}</strong>
                        </div>
                    </div>`).join('')}
                </div>
            </div>
        </section>`;

    case 'Pricing':
      const plans = [
        { name: "Basic", price: "$29", features: ["Feature 1", "Feature 2", "Email Support"] },
        { name: "Professional", price: "$59", features: ["Everything in Basic", "Feature 3", "Priority Support"] },
        { name: "Enterprise", price: "$99", features: ["Everything in Pro", "Custom Features", "24/7 Support"] }
      ];
      return `<section class="pricing" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>Our Pricing Plans</h2>
                <div class="pricing-grid">
                    ${plans.map((plan, index) => `
                    <div class="pricing-card ${index === 1 ? 'featured' : ''}">
                        <h3>${plan.name}</h3>
                        <div class="price">${plan.price}<span>/month</span></div>
                        <ul class="features">
                            ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                        <button class="btn ${index === 1 ? 'btn-primary' : 'btn-outline'}">Choose Plan</button>
                    </div>`).join('')}
                </div>
            </div>
        </section>`;

    default:
      return `<section class="${id}" data-section-id="${id}" data-section-name="${feature}" id="${id}">
            <div class="container">
                <h2>${feature}</h2>
                <p>This section showcases our ${feature.toLowerCase()} at ${businessName}.</p>
            </div>
        </section>`;
  }
};

// Get business-specific services
const getBusinessServices = (businessType: string) => {
  const services = {
    restaurant: [
      { title: 'Fine Dining', description: 'Exquisite cuisine prepared by expert chefs with premium ingredients', icon: '🍽️' },
      { title: 'Catering Services', description: 'Professional catering for your special events and celebrations', icon: '🎉' },
      { title: 'Private Events', description: 'Exclusive dining experiences for intimate gatherings', icon: '🥂' },
      { title: 'Takeout & Delivery', description: 'Enjoy our delicious meals from the comfort of your home', icon: '🚚' }
    ],
    portfolio: [
      { title: 'Web Development', description: 'Modern, responsive websites and web applications', icon: '💻' },
      { title: 'UI/UX Design', description: 'User-centered design solutions that engage and convert', icon: '🎨' },
      { title: 'Brand Identity', description: 'Complete branding solutions from concept to execution', icon: '✨' },
      { title: 'Consulting', description: 'Strategic guidance for your digital transformation', icon: '💡' }
    ],
    ecommerce: [
      { title: 'Online Shopping', description: 'Browse our extensive catalog of premium products', icon: '🛒' },
      { title: 'Fast Shipping', description: 'Quick and reliable delivery to your doorstep', icon: '📦' },
      { title: 'Customer Support', description: '24/7 assistance for all your shopping needs', icon: '💬' },
      { title: 'Secure Payments', description: 'Safe and encrypted payment processing', icon: '🔒' }
    ],
    agency: [
      { title: 'Strategy Development', description: 'Comprehensive business strategy and planning', icon: '📈' },
      { title: 'Digital Marketing', description: 'Data-driven marketing campaigns that deliver results', icon: '📱' },
      { title: 'Brand Management', description: 'Build and maintain a strong brand presence', icon: '🏆' },
      { title: 'Analytics & Insights', description: 'Deep insights to drive informed decisions', icon: '📊' }
    ]
  };

  return services[businessType] || [
    { title: 'Professional Service', description: 'High-quality service tailored to your specific needs', icon: '⭐' },
    { title: 'Expert Consultation', description: 'Get expert advice from our experienced team', icon: '🎯' },
    { title: 'Custom Solutions', description: 'Tailored solutions designed specifically for you', icon: '🔧' },
    { title: 'Ongoing Support', description: 'Comprehensive support to ensure your success', icon: '🤝' }
  ];
};

// Generate comprehensive CSS with modern styling
const generateDynamicCSS = (analysis: any): string => {
  const { businessType, colors, style, audience } = analysis;
  
  // Determine color scheme based on analysis
  const primaryColor = getColorValue(colors[0] || 'blue');
  const secondaryColor = getColorValue(colors[1] || 'white');
  const accentColor = getColorValue(colors[2] || 'gray');
  
  // Generate comprehensive modern CSS
  return `/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

h1 { font-size: 3rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 2rem; }
h4 { font-size: 1.5rem; }

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.7;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, ${primaryColor}, ${darkenColor(primaryColor, 20)});
    color: white;
    border-color: ${primaryColor};
}

.btn-primary:hover {
    background: linear-gradient(135deg, ${darkenColor(primaryColor, 10)}, ${darkenColor(primaryColor, 30)});
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.btn-secondary {
    background: transparent;
    color: ${primaryColor};
    border-color: ${primaryColor};
}

.btn-secondary:hover {
    background: ${primaryColor};
    color: white;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: ${primaryColor};
    border-color: ${primaryColor};
    padding: 10px 20px;
}

.btn-outline:hover {
    background: ${primaryColor};
    color: white;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo h1 {
    color: ${primaryColor};
    font-size: 1.8rem;
    margin: 0;
    font-weight: 800;
}

.nav {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: ${primaryColor};
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: ${primaryColor};
}

.nav-link:hover::after {
    width: 100%;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: ${primaryColor};
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, ${primaryColor}15, ${secondaryColor}15);
    padding: 120px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, ${primaryColor}10, transparent 70%);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    color: #333;
    line-height: 1.1;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: #666;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Section Styles */
section {
    padding: 80px 0;
}

section:nth-child(even) {
    background: #f8f9fa;
}

section h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: ${primaryColor};
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 1.5rem;
}

.about-stats {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.stat {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
}

.stat:hover {
    transform: translateY(-5px);
}

.stat h3 {
    font-size: 2.5rem;
    color: ${primaryColor};
    margin-bottom: 0.5rem;
}

.stat p {
    color: #666;
    font-weight: 500;
    margin: 0;
}

/* Services Section */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.service-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    color: ${primaryColor};
}

.service-card h3 {
    color: #333;
    margin-bottom: 1rem;
}

.service-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Contact Section */
.contact {
    background: linear-gradient(135deg, ${primaryColor}05, ${secondaryColor}05);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h3 {
    color: #333;
    margin-bottom: 1rem;
}

.contact-info p {
    color: #666;
    margin-bottom: 2rem;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #666;
}

.contact-item .icon {
    font-size: 1.2rem;
    color: ${primaryColor};
}

.contact-form {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: ${primaryColor};
}

.contact-form textarea {
    resize: vertical;
    min-height: 120px;
}

.contact-form button {
    width: 100%;
    margin-top: 1rem;
}

/* Gallery Section */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 3rem;
}

.gallery-item {
    aspect-ratio: 1;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, ${primaryColor}20, ${secondaryColor}20);
    display: flex;
    align-items: center;
    justify-content: center;
    color: ${primaryColor};
    font-weight: 600;
    font-size: 1.1rem;
}

/* Footer */
footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1.5rem;
    color: ${primaryColor};
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: ${primaryColor};
}

.footer-bottom {
    border-top: 1px solid #333;
    padding-top: 2rem;
    text-align: center;
    color: #888;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .nav {
        display: none;
    }
    
    .hero h1 {
        font-size: 2.5rem;
    }
    
    .hero p {
        font-size: 1.1rem;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-stats {
        flex-direction: row;
        justify-content: space-around;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.8rem; }
    h3 { font-size: 1.5rem; }
    
    section {
        padding: 50px 0;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero h1 {
        font-size: 2rem;
    }
    
    .contact-form {
        padding: 1.5rem;
    }
    
    .stat {
        padding: 1.5rem;
    }
    
    .service-card {
        padding: 2rem;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scroll animations will be handled by JavaScript */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}`;
};

// Helper function to get color values
const getColorValue = (colorName: string): string => {
  const colorMap: {[key: string]: string} = {
    'blue': '#3B82F6',
    'red': '#EF4444', 
    'green': '#10B981',
    'purple': '#8B5CF6',
    'yellow': '#F59E0B',
    'orange': '#F97316',
    'pink': '#EC4899',
    'black': '#1F2937',
    'white': '#FFFFFF',
    'gray': '#6B7280',
    'grey': '#6B7280',
    'navy': '#1E3A8A',
    'teal': '#14B8A6',
    'cyan': '#06B6D4',
    'indigo': '#6366F1',
    'violet': '#8B5CF6',
    'crimson': '#DC2626',
    'emerald': '#059669',
    'amber': '#D97706',
    'lime': '#65A30D',
    'rose': '#F43F5E',
    'sky': '#0EA5E9',
    'slate': '#475569'
  };
  
  return colorMap[colorName.toLowerCase()] || '#3B82F6';
};

// Helper function to darken colors
const darkenColor = (hex: string, percent: number): string => {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
};

// Enhanced JavaScript generation
const generateDynamicJS = (analysis: any): string => {
  return `// Enhanced Website Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Mobile menu toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const nav = document.querySelector('.nav');
    
    if (menuToggle && nav) {
        menuToggle.addEventListener('click', function() {
            nav.classList.toggle('active');
        });
    }
    
    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
            }
        });
    }, observerOptions);
    
    // Observe all elements with animation class
    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
    
    // Form validation and submission
    const contactForm = document.querySelector('.contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Basic form validation
            const inputs = this.querySelectorAll('input[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.style.borderColor = '#ef4444';
                    isValid = false;
                } else {
                    input.style.borderColor = '#e1e5e9';
                }
            });
            
            // Email validation
            const emailInput = this.querySelector('input[type="email"]');
            if (emailInput) {
                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;
                if (!emailRegex.test(emailInput.value)) {
                    emailInput.style.borderColor = '#ef4444';
                    isValid = false;
                }
            }
            
            if (isValid) {
                // Show success message
                const button = this.querySelector('button[type="submit"]');
                const originalText = button.textContent;
                button.textContent = 'Message Sent!';
                button.style.backgroundColor = '#10b981';
                
                // Reset form
                this.reset();
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.backgroundColor = '';
                }, 3000);
            } else {
                // Show error message
                alert('Please fill in all required fields correctly.');
            }
        });
    }
    
    // Header scroll effect
    const header = document.querySelector('header');
    if (header) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0,0,0,0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });
    }
    
    // Button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // Add scroll animations to sections
    document.querySelectorAll('section').forEach(section => {
        section.classList.add('animate-on-scroll');
    });
    
    console.log('Website initialized successfully!');
});`;
};

// Custom generation functions that respond to user prompts
const generateCustomHTML = (prompt: string, analysis: any): string => {
  const { businessName, businessType, features, colorScheme } = analysis;

  // Extract key information from the prompt
  const isLandingPage = prompt.toLowerCase().includes('landing') || prompt.toLowerCase().includes('single page');
  const isPortfolio = prompt.toLowerCase().includes('portfolio') || prompt.toLowerCase().includes('showcase');
  const isBlog = prompt.toLowerCase().includes('blog') || prompt.toLowerCase().includes('article');
  const isEcommerce = prompt.toLowerCase().includes('shop') || prompt.toLowerCase().includes('store') || prompt.toLowerCase().includes('ecommerce');

  // Generate title based on prompt
  let title = businessName;
  if (prompt.toLowerCase().includes('restaurant')) title = `${businessName} - Fine Dining Experience`;
  else if (prompt.toLowerCase().includes('portfolio')) title = `${businessName} - Creative Portfolio`;
  else if (prompt.toLowerCase().includes('blog')) title = `${businessName} - Blog & Insights`;
  else if (prompt.toLowerCase().includes('shop')) title = `${businessName} - Online Store`;

  // Generate hero section based on prompt content
  let heroContent = '';
  if (prompt.toLowerCase().includes('modern') || prompt.toLowerCase().includes('minimalist')) {
    heroContent = `
      <section class="hero modern-hero" data-section-id="hero" data-section-name="Hero Section">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title">${businessName}</h1>
            <p class="hero-subtitle">Modern. Elegant. Exceptional.</p>
            <p class="hero-description">Experience the future of ${businessType} with our innovative approach and cutting-edge solutions.</p>
            <div class="hero-actions">
              <button class="btn btn-primary">Get Started</button>
              <button class="btn btn-secondary">Learn More</button>
            </div>
          </div>
        </div>
      </section>`;
  } else if (prompt.toLowerCase().includes('creative') || prompt.toLowerCase().includes('artistic')) {
    heroContent = `
      <section class="hero creative-hero" data-section-id="hero" data-section-name="Hero Section">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title creative-title">${businessName}</h1>
            <p class="hero-subtitle">Where Creativity Meets Excellence</p>
            <p class="hero-description">Unleash your imagination with our unique ${businessType} services that bring your vision to life.</p>
            <div class="hero-actions">
              <button class="btn btn-creative">Explore Work</button>
              <button class="btn btn-outline">Contact Us</button>
            </div>
          </div>
        </div>
      </section>`;
  } else {
    heroContent = `
      <section class="hero" data-section-id="hero" data-section-name="Hero Section">
        <div class="container">
          <div class="hero-content">
            <h1 class="hero-title">${businessName}</h1>
            <p class="hero-subtitle">Excellence in ${businessType}</p>
            <p class="hero-description">${prompt.length > 50 ? prompt.substring(0, 100) + '...' : 'Delivering exceptional services tailored to your needs.'}</p>
            <div class="hero-actions">
              <button class="btn btn-primary">Get Started</button>
              <button class="btn btn-secondary">Learn More</button>
            </div>
          </div>
        </div>
      </section>`;
  }

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <div class="nav-brand">
                    <h2>${businessName}</h2>
                </div>
                <ul class="nav-menu">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
                <div class="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>

    <main>
        ${heroContent}

        ${features.includes('About Section') ? generateAboutSection(businessName, businessType, prompt) : ''}
        ${features.includes('Services') ? generateServicesSection(businessName, businessType, prompt) : ''}
        ${features.includes('Gallery') ? generateGallerySection(prompt) : ''}
        ${features.includes('Contact Form') ? generateContactSection() : ''}
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>${businessName}</h3>
                    <p>Excellence in ${businessType}</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="#home">Home</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="#services">Services</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <p>Email: info@${businessName.toLowerCase().replace(/\s+/g, '')}.com</p>
                    <p>Phone: (*************</p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 ${businessName}. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>`;
};

const generateAboutSection = (businessName: string, businessType: string, prompt: string): string => {
  const isPersonal = prompt.toLowerCase().includes('personal') || prompt.toLowerCase().includes('freelance');
  const isTeam = prompt.toLowerCase().includes('team') || prompt.toLowerCase().includes('agency');

  if (isPersonal) {
    return `
      <section class="about personal-about" data-section-id="about" data-section-name="About Section">
        <div class="container">
          <h2>About Me</h2>
          <div class="about-content">
            <div class="about-text">
              <p>Hi, I'm passionate about ${businessType} and dedicated to creating exceptional experiences for my clients.</p>
              <p>With years of experience and a commitment to excellence, I bring creativity and professionalism to every project.</p>
            </div>
            <div class="about-image">
              <div class="placeholder-image">Your Photo Here</div>
            </div>
          </div>
        </div>
      </section>`;
  } else {
    return `
      <section class="about" data-section-id="about" data-section-name="About Section">
        <div class="container">
          <h2>About ${businessName}</h2>
          <div class="about-content">
            <div class="about-text">
              <p>At ${businessName}, we are passionate about delivering exceptional ${businessType} services that exceed your expectations.</p>
              <p>Our team is dedicated to providing you with the best possible experience, combining innovation with reliability.</p>
            </div>
            <div class="about-stats">
              <div class="stat">
                <h3>500+</h3>
                <p>Happy Clients</p>
              </div>
              <div class="stat">
                <h3>5+</h3>
                <p>Years Experience</p>
              </div>
              <div class="stat">
                <h3>100%</h3>
                <p>Satisfaction</p>
              </div>
            </div>
          </div>
        </div>
      </section>`;
  }
};

const generateServicesSection = (businessName: string, businessType: string, prompt: string): string => {
  // Generate services based on the prompt content
  let services = [];

  if (prompt.toLowerCase().includes('web') || prompt.toLowerCase().includes('website')) {
    services = [
      { title: 'Web Design', description: 'Custom website design tailored to your brand' },
      { title: 'Web Development', description: 'Full-stack development with modern technologies' },
      { title: 'SEO Optimization', description: 'Improve your search engine rankings' }
    ];
  } else if (prompt.toLowerCase().includes('restaurant') || prompt.toLowerCase().includes('food')) {
    services = [
      { title: 'Fine Dining', description: 'Exquisite culinary experiences' },
      { title: 'Catering', description: 'Professional catering for events' },
      { title: 'Private Events', description: 'Exclusive venue for special occasions' }
    ];
  } else if (prompt.toLowerCase().includes('photography') || prompt.toLowerCase().includes('photo')) {
    services = [
      { title: 'Portrait Photography', description: 'Professional portrait sessions' },
      { title: 'Event Photography', description: 'Capture your special moments' },
      { title: 'Commercial Photography', description: 'Business and product photography' }
    ];
  } else {
    services = [
      { title: 'Consultation', description: 'Expert advice and planning' },
      { title: 'Implementation', description: 'Professional execution of solutions' },
      { title: 'Support', description: 'Ongoing support and maintenance' }
    ];
  }

  const serviceCards = services.map(service => `
    <div class="service-card">
      <h3>${service.title}</h3>
      <p>${service.description}</p>
      <button class="btn btn-outline">Learn More</button>
    </div>
  `).join('');

  return `
    <section class="services" data-section-id="services" data-section-name="Services Section">
      <div class="container">
        <h2>Our Services</h2>
        <p class="section-subtitle">Discover what we can do for you</p>
        <div class="services-grid">
          ${serviceCards}
        </div>
      </div>
    </section>`;
};

const generateGallerySection = (prompt: string): string => {
  const isPortfolio = prompt.toLowerCase().includes('portfolio') || prompt.toLowerCase().includes('work');
  const title = isPortfolio ? 'Our Work' : 'Gallery';

  return `
    <section class="gallery" data-section-id="gallery" data-section-name="Gallery Section">
      <div class="container">
        <h2>${title}</h2>
        <p class="section-subtitle">Showcasing our best work</p>
        <div class="gallery-grid">
          <div class="gallery-item">
            <div class="placeholder-image">Project 1</div>
          </div>
          <div class="gallery-item">
            <div class="placeholder-image">Project 2</div>
          </div>
          <div class="gallery-item">
            <div class="placeholder-image">Project 3</div>
          </div>
          <div class="gallery-item">
            <div class="placeholder-image">Project 4</div>
          </div>
        </div>
      </div>
    </section>`;
};

const generateContactSection = (): string => {
  return `
    <section class="contact" data-section-id="contact" data-section-name="Contact Section">
      <div class="container">
        <h2>Get In Touch</h2>
        <p class="section-subtitle">We'd love to hear from you</p>
        <div class="contact-content">
          <div class="contact-form">
            <form>
              <div class="form-group">
                <input type="text" placeholder="Your Name" required>
              </div>
              <div class="form-group">
                <input type="email" placeholder="Your Email" required>
              </div>
              <div class="form-group">
                <textarea placeholder="Your Message" rows="5" required></textarea>
              </div>
              <button type="submit" class="btn btn-primary">Send Message</button>
            </form>
          </div>
          <div class="contact-info">
            <div class="contact-item">
              <h4>Email</h4>
              <p><EMAIL></p>
            </div>
            <div class="contact-item">
              <h4>Phone</h4>
              <p>(*************</p>
            </div>
            <div class="contact-item">
              <h4>Address</h4>
              <p>123 Business St, City, State 12345</p>
            </div>
          </div>
        </div>
      </div>
    </section>`;
};

export const generateCode = async (options: GenerateOptions): Promise<GeneratedFile[]> => {
  const { prompt, model, apiKey } = options;

  console.log('Generating website for prompt:', prompt, 'using model:', model);

  if (!apiKey) {
    throw new Error(`API key is required for ${model}. Please configure your API key in settings.`);
  }

  try {
    // Create the system prompt and user prompt
    const systemPrompt = getSystemPrompt();
    const userPrompt = `${GENERATION_PROMPT}\n\nUser Request: ${prompt}\n\nPlease generate a complete website with the following files:\n1. index.html - Complete HTML structure\n2. styles.css - Complete CSS styling\n3. script.js - JavaScript functionality (if needed)\n\nMake sure to include all code within proper code blocks and clearly separate each file.`;

    // Call the AI API
    const response = await callAI({
      apiKey,
      model,
      systemPrompt,
      userPrompt,
    });

    console.log('AI Response received, parsing files...');

    // Parse the AI response to extract files
    const files = parseAIResponse(response.content);

    if (files.length === 0) {
      throw new Error('No valid files were generated. Please try rephrasing your request.');
    }

    console.log('Generated files:', files.map(f => ({ name: f.name, contentLength: f.content?.length })));

    return files;
  } catch (error) {
    console.error('Error generating website:', error);

    // Fallback to template-based generation if AI fails
    console.log('Falling back to template-based generation...');
    const analysis = analyzePrompt(prompt);
    const htmlContent = generateCustomHTML(prompt, analysis);
    const cssContent = generateCustomCSS(prompt, analysis);
    const jsContent = generateCustomJS(prompt, analysis);

    return [
      {
        id: generateUniqueId(),
        name: 'index.html',
        content: htmlContent,
        language: 'html'
      },
      {
        id: generateUniqueId(),
        name: 'styles.css',
        content: cssContent,
        language: 'css'
      },
      {
        id: generateUniqueId(),
        name: 'script.js',
        content: jsContent,
        language: 'javascript'
      }
    ];
  }
};

// Custom CSS generation based on prompt
const generateCustomCSS = (prompt: string, analysis: any): string => {
  const { colorScheme } = analysis;

  // Determine style based on prompt keywords
  const isModern = prompt.toLowerCase().includes('modern') || prompt.toLowerCase().includes('minimalist');
  const isCreative = prompt.toLowerCase().includes('creative') || prompt.toLowerCase().includes('artistic');
  const isDark = prompt.toLowerCase().includes('dark') || prompt.toLowerCase().includes('black');
  const isColorful = prompt.toLowerCase().includes('colorful') || prompt.toLowerCase().includes('vibrant');

  // Base colors
  let primaryColor = '#007bff';
  let secondaryColor = '#6c757d';
  let backgroundColor = '#ffffff';
  let textColor = '#333333';

  if (isDark) {
    backgroundColor = '#1a1a1a';
    textColor = '#ffffff';
    primaryColor = '#4dabf7';
  } else if (isColorful) {
    primaryColor = '#ff6b6b';
    secondaryColor = '#4ecdc4';
  } else if (isCreative) {
    primaryColor = '#9c27b0';
    secondaryColor = '#ff9800';
  }

  return `/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    color: ${textColor};
    background-color: ${backgroundColor};
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: ${backgroundColor};
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    color: ${primaryColor};
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: ${textColor};
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-menu a:hover {
    color: ${primaryColor};
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: ${textColor};
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, ${primaryColor}10, ${secondaryColor}10);
    text-align: center;
}

${isModern ? `
.modern-hero {
    background: ${backgroundColor};
    border-bottom: 1px solid #eee;
}

.modern-hero .hero-title {
    font-size: 3.5rem;
    font-weight: 300;
    letter-spacing: -2px;
}
` : ''}

${isCreative ? `
.creative-hero {
    background: linear-gradient(45deg, ${primaryColor}, ${secondaryColor});
    color: white;
}

.creative-title {
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}
` : ''}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: ${textColor};
}

.hero-subtitle {
    font-size: 1.5rem;
    color: ${primaryColor};
    margin-bottom: 1rem;
    font-weight: 600;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.8;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Button Styles */
.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: ${primaryColor};
    color: white;
}

.btn-primary:hover {
    background: ${primaryColor}dd;
    transform: translateY(-2px);
}

.btn-secondary {
    background: ${secondaryColor};
    color: white;
}

.btn-secondary:hover {
    background: ${secondaryColor}dd;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: ${primaryColor};
    border: 2px solid ${primaryColor};
}

.btn-outline:hover {
    background: ${primaryColor};
    color: white;
}

${isCreative ? `
.btn-creative {
    background: linear-gradient(45deg, ${primaryColor}, ${secondaryColor});
    color: white;
    border: none;
}

.btn-creative:hover {
    transform: translateY(-2px) scale(1.05);
}
` : ''}

/* Section Styles */
section {
    padding: 80px 0;
}

section h2 {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: ${textColor};
}

.section-subtitle {
    text-align: center;
    font-size: 1.1rem;
    color: ${textColor}aa;
    margin-bottom: 3rem;
}

/* About Section */
.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.about-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    text-align: center;
}

.stat h3 {
    font-size: 2.5rem;
    color: ${primaryColor};
    margin-bottom: 0.5rem;
}

.placeholder-image {
    background: ${primaryColor}20;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: ${primaryColor};
    font-weight: 600;
}

/* Services Section */
.services {
    background: ${backgroundColor === '#1a1a1a' ? '#222' : '#f8f9fa'};
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: ${backgroundColor};
    padding: 2rem;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card h3 {
    color: ${primaryColor};
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.service-card p {
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Gallery Section */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.gallery-item {
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #eee;
    border-radius: 6px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: ${primaryColor};
}

.contact-item {
    margin-bottom: 2rem;
}

.contact-item h4 {
    color: ${primaryColor};
    margin-bottom: 0.5rem;
}

/* Footer */
.footer {
    background: ${backgroundColor === '#1a1a1a' ? '#111' : '#333'};
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: ${primaryColor};
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: ${primaryColor};
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #555;
    color: #ccc;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .nav-toggle {
        display: flex;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
    }

    .about-stats {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease-out;
}`;
};

// Custom JavaScript generation
const generateCustomJS = (prompt: string, analysis: any): string => {
  const hasContactForm = prompt.toLowerCase().includes('contact') || prompt.toLowerCase().includes('form');
  const hasGallery = prompt.toLowerCase().includes('gallery') || prompt.toLowerCase().includes('portfolio');

  return `// Website functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('Website loaded successfully!');

    // Mobile navigation toggle
    const navToggle = document.querySelector('.nav-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navMenu.classList.toggle('active');
        });
    }

    // Smooth scrolling for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    ${hasContactForm ? `
    // Contact form handling
    const contactForm = document.querySelector('.contact-form form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const name = this.querySelector('input[type="text"]').value;
            const email = this.querySelector('input[type="email"]').value;
            const message = this.querySelector('textarea').value;

            // Simple validation
            if (!name || !email || !message) {
                alert('Please fill in all fields');
                return;
            }

            // Simulate form submission
            alert('Thank you for your message! We\\'ll get back to you soon.');
            this.reset();
        });
    }
    ` : ''}

    ${hasGallery ? `
    // Gallery functionality
    const galleryItems = document.querySelectorAll('.gallery-item');
    galleryItems.forEach(item => {
        item.addEventListener('click', function() {
            // Simple lightbox effect
            const rect = this.getBoundingClientRect();
            this.style.transform = 'scale(1.1)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 300);
        });
    });
    ` : ''}

    // Scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-on-scroll');
            }
        });
    }, observerOptions);

    // Observe all sections
    document.querySelectorAll('section').forEach(section => {
        observer.observe(section);
    });

    // Button hover effects
    document.querySelectorAll('.btn').forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Service card interactions
    document.querySelectorAll('.service-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 6px rgba(0,0,0,0.1)';
        });
    });

    console.log('All interactive features initialized!');
});`;
};

export const modifyWebsiteFromChat = async ({
  prompt,
  currentFiles,
  chatHistory,
  model,
  apiKey
}: ChatModificationOptions & { apiKey?: string }): Promise<GeneratedFile[]> => {
  try {
    console.log('Modifying website based on chat:', prompt);

    // If we have an API key, try to use AI for modifications
    if (apiKey && model) {
      try {
        const systemPrompt = getSystemPrompt();
        const currentHtml = currentFiles.find(f => f.name.endsWith('.html'))?.content || '';
        const currentCss = currentFiles.find(f => f.name.endsWith('.css'))?.content || '';
        const currentJs = currentFiles.find(f => f.name.endsWith('.js'))?.content || '';

        const userPrompt = `${GENERATION_PROMPT}\n\nI have an existing website and need to modify it based on this request: "${prompt}"\n\nCurrent files:\n\nindex.html:\n${currentHtml}\n\nstyles.css:\n${currentCss}\n\nscript.js:\n${currentJs}\n\nPlease provide the updated files with the requested modifications. Make sure to maintain the existing structure while implementing the changes.`;

        const response = await callAI({
          apiKey,
          model: model as AIModel,
          systemPrompt,
          userPrompt,
        });

        const updatedFiles = parseAIResponse(response.content);
        if (updatedFiles.length > 0) {
          console.log('Website modified successfully using AI');
          return updatedFiles;
        }
      } catch (error) {
        console.warn('AI modification failed, falling back to template-based modification:', error);
      }
    }

    // Fallback to template-based modification
    const lowerPrompt = prompt.toLowerCase();
    
    // Find the HTML file
    const htmlFile = currentFiles.find(file => file.name.endsWith('.html'));
    const cssFile = currentFiles.find(file => file.name.endsWith('.css'));
    const jsFile = currentFiles.find(file => file.name.endsWith('.js'));
    
    if (!htmlFile) {
      throw new Error("HTML file not found");
    }

    let updatedFiles = [...currentFiles];
    
    // Handle different types of modifications
    if (lowerPrompt.includes('color') || lowerPrompt.includes('theme')) {
      // Color/theme changes
      const analysis = analyzePrompt(prompt);
      const newCSS = generateDynamicCSS(analysis);
      updatedFiles = updatedFiles.map(file => 
        file.name.endsWith('.css') ? { ...file, content: newCSS } : file
      );
    }
    
    else if (lowerPrompt.includes('add') && (lowerPrompt.includes('section') || lowerPrompt.includes('page'))) {
      // Add new sections
      const analysis = analyzePrompt(prompt);
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlFile.content, 'text/html');
      const main = doc.querySelector('main');
      
      if (main) {
        // Generate new section based on prompt
        const newSectionId = 'new-section-' + Date.now();
        const newSectionHTML = generateFeatureSection('Custom Section', newSectionId, {
          ...analysis,
          description: prompt
        });
        
        const footer = doc.querySelector('footer');
        if (footer) {
          footer.insertAdjacentHTML('beforebegin', newSectionHTML);
        } else {
          main.insertAdjacentHTML('beforeend', newSectionHTML);
        }
        
        const updatedHtmlContent = '<!DOCTYPE html>\n' + doc.documentElement.outerHTML;
        updatedFiles = updatedFiles.map(file => 
          file.name.endsWith('.html') ? { ...file, content: updatedHtmlContent } : file
        );
      }
    }
    
    else if (lowerPrompt.includes('change') || lowerPrompt.includes('modify') || lowerPrompt.includes('update')) {
      // Modify existing content
      const analysis = analyzePrompt(prompt);
      
      // Re-analyze the original content and apply changes
      const originalAnalysis = analyzePrompt(chatHistory[0]?.content || 'business website');
      const mergedAnalysis = {
        ...originalAnalysis,
        ...analysis,
        description: prompt
      };
      
      const newHTML = generateDynamicHTML(mergedAnalysis);
      const newCSS = generateDynamicCSS(mergedAnalysis);
      
      updatedFiles = updatedFiles.map(file => {
        if (file.name.endsWith('.html')) {
          return { ...file, content: newHTML };
        } else if (file.name.endsWith('.css')) {
          return { ...file, content: newCSS };
        }
        return file;
      });
    }
    
    else {
      // General content updates
      const analysis = analyzePrompt(prompt);
      const originalAnalysis = analyzePrompt(chatHistory[0]?.content || 'business website');
      
      // Merge the analyses to preserve original intent while applying changes
      const mergedAnalysis = {
        ...originalAnalysis,
        businessName: analysis.businessName || originalAnalysis.businessName,
        businessType: analysis.businessType !== 'business' ? analysis.businessType : originalAnalysis.businessType,
        features: analysis.features.length > 0 ? [...new Set([...originalAnalysis.features, ...analysis.features])] : originalAnalysis.features,
        colors: analysis.colors.length > 0 ? analysis.colors : originalAnalysis.colors,
        style: analysis.style !== 'modern' ? analysis.style : originalAnalysis.style,
        description: prompt
      };
      
      const newHTML = generateDynamicHTML(mergedAnalysis);
      const newCSS = generateDynamicCSS(mergedAnalysis);
      const newJS = generateDynamicJS(mergedAnalysis);
      
      updatedFiles = updatedFiles.map(file => {
        if (file.name.endsWith('.html')) {
          return { ...file, content: newHTML };
        } else if (file.name.endsWith('.css')) {
          return { ...file, content: newCSS };
        } else if (file.name.endsWith('.js')) {
          return { ...file, content: newJS };
        }
        return file;
      });
    }
    
    console.log('Website modified successfully based on chat input');
    return updatedFiles;
    
  } catch (error) {
    console.error("Error modifying website:", error);
    throw new Error("Failed to modify website. Please try rephrasing your request.");
  }
};

export const regenerateSection = async ({
  originalPrompt,
  sectionPrompt,
  sectionId,
  currentFiles,
  model
}: RegenerateSectionOptions): Promise<GeneratedFile[]> => {
  try {
    console.log('Regenerating section:', sectionId, 'with prompt:', sectionPrompt);
    
    // Find the HTML file
    const htmlFile = currentFiles.find(file => file.name.endsWith('.html'));
    
    if (!htmlFile) {
      throw new Error("HTML file not found");
    }
    
    // Analyze the new prompt for the section
    const analysis = analyzePrompt(sectionPrompt);
    const originalAnalysis = analyzePrompt(originalPrompt);
    
    // Merge analyses to maintain consistency
    const mergedAnalysis = {
      ...originalAnalysis,
      ...analysis,
      description: sectionPrompt
    };
    
    // Parse and update the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlFile.content, 'text/html');
    const sectionToRegenerate = doc.querySelector(`[data-section-id="${sectionId}"]`);
    
    if (!sectionToRegenerate) {
      throw new Error(`Section with ID ${sectionId} not found`);
    }
    
    const sectionName = sectionToRegenerate.getAttribute('data-section-name') || 'Section';
    
    // Generate new section content based on the new prompt
    const newSectionHTML = generateFeatureSection(sectionName, sectionId, mergedAnalysis);
    
    sectionToRegenerate.outerHTML = newSectionHTML;
    const updatedHtmlContent = '<!DOCTYPE html>\n' + doc.documentElement.outerHTML;
    
    return currentFiles.map(file => {
      if (file.name.endsWith('.html')) {
        return { ...file, content: updatedHtmlContent };
      }
      return file;
    });
  } catch (error) {
    console.error("Error regenerating section:", error);
    throw new Error("Failed to regenerate section. Please try again with a more specific description.");
  }
};
