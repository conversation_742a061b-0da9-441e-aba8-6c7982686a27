<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Integration Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        input, textarea, select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #3b82f6;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
        }
        .error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }
        .success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 12px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI Integration Test</h1>
        <p>Test the AI API integration with your configured API keys.</p>
        
        <form id="testForm">
            <div class="form-group">
                <label for="model">AI Model:</label>
                <select id="model" required>
                    <option value="openai">OpenAI (GPT-4)</option>
                    <option value="anthropic">Anthropic Claude</option>
                    <option value="google">Google Gemini</option>
                    <option value="mistral">Mistral</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="apiKey">API Key:</label>
                <input type="password" id="apiKey" placeholder="Enter your API key..." required>
                <small style="color: #6b7280; font-size: 12px;">Your API key is not stored and only used for this test.</small>
            </div>
            
            <div class="form-group">
                <label for="prompt">Test Prompt:</label>
                <textarea id="prompt" rows="3" placeholder="Create a simple landing page for a coffee shop..." required>Create a simple landing page for a coffee shop with a hero section, menu preview, and contact information.</textarea>
            </div>
            
            <button type="submit" id="submitBtn">Test AI Generation</button>
        </form>
        
        <div id="result" style="display: none;"></div>
    </div>

    <script type="module">
        // Import the AI service functions
        import { callAI } from './src/services/apiClients.js';
        import { getSystemPrompt, GENERATION_PROMPT } from './src/constants/prompts.js';

        const form = document.getElementById('testForm');
        const resultDiv = document.getElementById('result');
        const submitBtn = document.getElementById('submitBtn');

        form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const model = document.getElementById('model').value;
            const apiKey = document.getElementById('apiKey').value;
            const prompt = document.getElementById('prompt').value;
            
            if (!apiKey.trim()) {
                showResult('Please enter an API key.', 'error');
                return;
            }
            
            submitBtn.disabled = true;
            submitBtn.textContent = 'Testing...';
            
            try {
                showResult('Calling AI API...', 'info');
                
                const systemPrompt = getSystemPrompt();
                const userPrompt = `${GENERATION_PROMPT}\n\nUser Request: ${prompt}`;
                
                const response = await callAI({
                    apiKey,
                    model,
                    systemPrompt,
                    userPrompt,
                });
                
                showResult(`✅ Success! AI responded with ${response.content.length} characters.`, 'success');
                
                // Show a preview of the response
                const preview = document.createElement('div');
                preview.innerHTML = `
                    <h3>Response Preview:</h3>
                    <pre>${response.content.substring(0, 500)}${response.content.length > 500 ? '...' : ''}</pre>
                `;
                resultDiv.appendChild(preview);
                
            } catch (error) {
                console.error('AI Test Error:', error);
                showResult(`❌ Error: ${error.message}`, 'error');
            } finally {
                submitBtn.disabled = false;
                submitBtn.textContent = 'Test AI Generation';
            }
        });

        function showResult(message, type) {
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.innerHTML = `<p>${message}</p>`;
        }
    </script>
</body>
</html>
