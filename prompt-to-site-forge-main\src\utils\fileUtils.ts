
import { GeneratedFile } from "../types";
import JS<PERSON><PERSON> from "jszip";

export function downloadAsZip(files: GeneratedFile[]): void {
  const zip = new JSZip();
  
  // Add all files to the zip
  files.forEach(file => {
    zip.file(file.name, file.content);
  });
  
  // Generate and download the zip
  zip.generateAsync({ type: "blob" }).then((blob) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "generated-website.zip";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  });
}

export function guessLanguage(filename: string): string {
  const extension = filename.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'html': return 'html';
    case 'js': return 'javascript';
    case 'jsx': return 'javascript';
    case 'ts': return 'typescript';
    case 'tsx': return 'typescript';
    case 'css': return 'css';
    case 'json': return 'json';
    case 'md': return 'markdown';
    case 'yml':
    case 'yaml': return 'yaml';
    default: return 'plaintext';
  }
}
